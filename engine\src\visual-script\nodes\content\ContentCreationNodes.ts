/**
 * 内容创作工具节点实现
 * 提供资源浏览、内容验证、资源优化、内容导出等功能
 */

import { VisualScriptNode } from '../VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * 资源信息接口
 */
export interface AssetInfo {
  id: string;
  name: string;
  type: string;
  path: string;
  size: number;
  lastModified: Date;
  metadata: Record<string, any>;
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

/**
 * 资源浏览器节点
 */
export class AssetBrowserNode extends VisualScriptNode {
  public static readonly TYPE = 'AssetBrowser';
  public static readonly NAME = '资源浏览器';
  public static readonly DESCRIPTION = '资源浏览器节点，提供资源管理和浏览功能';

  private assets: Map<string, AssetInfo> = new Map();
  private currentPath: string = '/';
  private selectedAssets: string[] = [];

  constructor(nodeType: string = AssetBrowserNode.TYPE, name: string = AssetBrowserNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
    this.initializeAssets();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInputPort('browse', 'trigger', '浏览资源');
    this.addInputPort('search', 'trigger', '搜索资源');
    this.addInputPort('select', 'trigger', '选择资源');
    this.addInputPort('path', 'string', '路径');
    this.addInputPort('searchQuery', 'string', '搜索查询');
    this.addInputPort('assetId', 'string', '资源ID');
    this.addInputPort('filterType', 'string', '过滤类型');

    // 输出端口
    this.addOutputPort('assetList', 'array', '资源列表');
    this.addOutputPort('selectedAsset', 'object', '选中资源');
    this.addOutputPort('currentPath', 'string', '当前路径');
    this.addOutputPort('assetCount', 'number', '资源数量');
    this.addOutputPort('onAssetSelected', 'trigger', '资源选中');
    this.addOutputPort('onPathChanged', 'trigger', '路径改变');
  }

  private initializeAssets(): void {
    // 初始化一些示例资源
    const sampleAssets: AssetInfo[] = [
      {
        id: 'model_001',
        name: 'Character.fbx',
        type: 'model',
        path: '/models/Character.fbx',
        size: 2048000,
        lastModified: new Date(),
        metadata: { vertices: 5000, materials: 3 }
      },
      {
        id: 'texture_001',
        name: 'Diffuse.png',
        type: 'texture',
        path: '/textures/Diffuse.png',
        size: 1024000,
        lastModified: new Date(),
        metadata: { width: 1024, height: 1024, format: 'PNG' }
      },
      {
        id: 'audio_001',
        name: 'Background.mp3',
        type: 'audio',
        path: '/audio/Background.mp3',
        size: 5120000,
        lastModified: new Date(),
        metadata: { duration: 180, bitrate: 320 }
      }
    ];

    sampleAssets.forEach(asset => {
      this.assets.set(asset.id, asset);
    });
  }

  public execute(inputs?: any): any {
    try {
      if (inputs?.browse) {
        return this.browseAssets(inputs);
      } else if (inputs?.search) {
        return this.searchAssets(inputs);
      } else if (inputs?.select) {
        return this.selectAsset(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('AssetBrowserNode', '执行失败', error);
      return this.getDefaultOutputs();
    }
  }

  private browseAssets(inputs: any): any {
    const path = inputs?.path as string || this.currentPath;
    const filterType = inputs?.filterType as string;

    this.currentPath = path;

    let assetList = Array.from(this.assets.values());

    // 应用路径过滤
    if (path !== '/') {
      assetList = assetList.filter(asset => asset.path.startsWith(path));
    }

    // 应用类型过滤
    if (filterType) {
      assetList = assetList.filter(asset => asset.type === filterType);
    }

    Debug.log('AssetBrowserNode', `浏览资源: ${path}, 找到 ${assetList.length} 个资源`);

    return {
      assetList,
      selectedAsset: null,
      currentPath: this.currentPath,
      assetCount: assetList.length,
      onAssetSelected: false,
      onPathChanged: true
    };
  }

  private searchAssets(inputs: any): any {
    const searchQuery = inputs?.searchQuery as string;

    if (!searchQuery) {
      Debug.warn('AssetBrowserNode', '搜索查询为空');
      return this.getDefaultOutputs();
    }

    const query = searchQuery.toLowerCase();
    const assetList = Array.from(this.assets.values()).filter(asset =>
      asset.name.toLowerCase().includes(query) ||
      asset.type.toLowerCase().includes(query) ||
      asset.path.toLowerCase().includes(query)
    );

    Debug.log('AssetBrowserNode', `搜索资源: "${searchQuery}", 找到 ${assetList.length} 个结果`);

    return {
      assetList,
      selectedAsset: null,
      currentPath: this.currentPath,
      assetCount: assetList.length,
      onAssetSelected: false,
      onPathChanged: false
    };
  }

  private selectAsset(inputs: any): any {
    const assetId = inputs?.assetId as string;

    if (!assetId || !this.assets.has(assetId)) {
      Debug.warn('AssetBrowserNode', `资源不存在: ${assetId}`);
      return this.getDefaultOutputs();
    }

    const selectedAsset = this.assets.get(assetId)!;
    this.selectedAssets = [assetId];

    Debug.log('AssetBrowserNode', `资源选中: ${selectedAsset.name}`);

    return {
      assetList: Array.from(this.assets.values()),
      selectedAsset,
      currentPath: this.currentPath,
      assetCount: this.assets.size,
      onAssetSelected: true,
      onPathChanged: false
    };
  }

  private getDefaultOutputs(): any {
    return {
      assetList: Array.from(this.assets.values()),
      selectedAsset: null,
      currentPath: this.currentPath,
      assetCount: this.assets.size,
      onAssetSelected: false,
      onPathChanged: false
    };
  }
}

/**
 * 内容验证节点
 */
export class ContentValidationNode extends VisualScriptNode {
  public static readonly TYPE = 'ContentValidation';
  public static readonly NAME = '内容验证';
  public static readonly DESCRIPTION = '内容验证节点，检查内容完整性和规范性';

  constructor(nodeType: string = ContentValidationNode.TYPE, name: string = ContentValidationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInputPort('validate', 'trigger', '执行验证');
    this.addInputPort('content', 'object', '内容对象');
    this.addInputPort('validationRules', 'array', '验证规则');
    this.addInputPort('strictMode', 'boolean', '严格模式');

    // 输出端口
    this.addOutputPort('validationResult', 'object', '验证结果');
    this.addOutputPort('isValid', 'boolean', '是否有效');
    this.addOutputPort('errorCount', 'number', '错误数量');
    this.addOutputPort('warningCount', 'number', '警告数量');
    this.addOutputPort('onValidated', 'trigger', '验证完成');
  }

  public execute(inputs?: any): any {
    try {
      if (inputs?.validate) {
        return this.validateContent(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('ContentValidationNode', '执行失败', error);
      return this.getDefaultOutputs();
    }
  }

  private validateContent(inputs: any): any {
    const content = inputs?.content;
    const validationRules = inputs?.validationRules as string[] || [];
    const strictMode = inputs?.strictMode as boolean || false;

    if (!content) {
      const result: ValidationResult = {
        isValid: false,
        errors: ['内容对象为空'],
        warnings: [],
        suggestions: []
      };

      return this.createValidationOutput(result);
    }

    const result = this.performValidation(content, validationRules, strictMode);

    Debug.log('ContentValidationNode', `内容验证完成: ${result.isValid ? '有效' : '无效'}, 错误: ${result.errors.length}, 警告: ${result.warnings.length}`);

    return this.createValidationOutput(result);
  }

  private performValidation(content: any, rules: string[], strictMode: boolean): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // 基本验证
    if (typeof content !== 'object') {
      errors.push('内容必须是对象类型');
    }

    // 规则验证
    rules.forEach(rule => {
      switch (rule) {
        case 'required_name':
          if (!content.name || content.name.trim() === '') {
            errors.push('缺少必需的名称字段');
          }
          break;
        case 'required_id':
          if (!content.id) {
            errors.push('缺少必需的ID字段');
          }
          break;
        case 'valid_type':
          if (!content.type) {
            warnings.push('建议添加类型字段');
          }
          break;
      }
    });

    // 严格模式验证
    if (strictMode) {
      if (!content.metadata) {
        warnings.push('建议添加元数据');
      }
      if (!content.version) {
        suggestions.push('考虑添加版本信息');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  private createValidationOutput(result: ValidationResult): any {
    return {
      validationResult: result,
      isValid: result.isValid,
      errorCount: result.errors.length,
      warningCount: result.warnings.length,
      onValidated: true
    };
  }

  private getDefaultOutputs(): any {
    return {
      validationResult: null,
      isValid: false,
      errorCount: 0,
      warningCount: 0,
      onValidated: false
    };
  }
}

/**
 * 资源优化节点
 */
export class AssetOptimizationNode extends VisualScriptNode {
  public static readonly TYPE = 'AssetOptimization';
  public static readonly NAME = '资源优化';
  public static readonly DESCRIPTION = '资源优化节点，优化资源文件大小和性能';

  constructor(nodeType: string = AssetOptimizationNode.TYPE, name: string = AssetOptimizationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInputPort('optimize', 'trigger', '执行优化');
    this.addInputPort('asset', 'object', '资源对象');
    this.addInputPort('optimizationLevel', 'string', '优化级别');
    this.addInputPort('targetPlatform', 'string', '目标平台');
    this.addInputPort('preserveQuality', 'boolean', '保持质量');

    // 输出端口
    this.addOutputPort('optimizedAsset', 'object', '优化资源');
    this.addOutputPort('optimizationReport', 'object', '优化报告');
    this.addOutputPort('sizeReduction', 'number', '大小减少');
    this.addOutputPort('onOptimized', 'trigger', '优化完成');
    this.addOutputPort('onError', 'trigger', '优化失败');
  }

  public execute(inputs?: any): any {
    try {
      if (inputs?.optimize) {
        return this.optimizeAsset(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('AssetOptimizationNode', '执行失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private optimizeAsset(inputs: any): any {
    const asset = inputs?.asset as AssetInfo;
    const optimizationLevel = inputs?.optimizationLevel as string || 'medium';
    const targetPlatform = inputs?.targetPlatform as string || 'desktop';
    const preserveQuality = inputs?.preserveQuality as boolean || true;

    if (!asset) {
      Debug.warn('AssetOptimizationNode', '未提供资源对象');
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }

    const optimizedAsset = this.performOptimization(asset, optimizationLevel, targetPlatform, preserveQuality);
    const sizeReduction = this.calculateSizeReduction(asset, optimizedAsset);
    
    const optimizationReport = {
      originalSize: asset.size,
      optimizedSize: optimizedAsset.size,
      compressionRatio: sizeReduction,
      optimizationLevel,
      targetPlatform,
      preserveQuality,
      timestamp: new Date().toISOString()
    };

    Debug.log('AssetOptimizationNode', `资源优化完成: ${asset.name}, 大小减少: ${sizeReduction}%`);

    return {
      optimizedAsset,
      optimizationReport,
      sizeReduction,
      onOptimized: true,
      onError: false
    };
  }

  private performOptimization(asset: AssetInfo, level: string, platform: string, preserveQuality: boolean): AssetInfo {
    const optimized = { ...asset };

    // 根据优化级别计算压缩比例
    let compressionRatio = 1.0;
    switch (level) {
      case 'low':
        compressionRatio = 0.9;
        break;
      case 'medium':
        compressionRatio = 0.7;
        break;
      case 'high':
        compressionRatio = preserveQuality ? 0.6 : 0.4;
        break;
    }

    // 根据平台调整
    if (platform === 'mobile') {
      compressionRatio *= 0.8;
    }

    optimized.size = Math.round(asset.size * compressionRatio);
    optimized.metadata = {
      ...asset.metadata,
      optimized: true,
      optimizationLevel: level,
      targetPlatform: platform
    };

    return optimized;
  }

  private calculateSizeReduction(original: AssetInfo, optimized: AssetInfo): number {
    return Math.round(((original.size - optimized.size) / original.size) * 100);
  }

  private getDefaultOutputs(): any {
    return {
      optimizedAsset: null,
      optimizationReport: null,
      sizeReduction: 0,
      onOptimized: false,
      onError: false
    };
  }
}

/**
 * 内容导出节点
 */
export class ContentExportNode extends VisualScriptNode {
  public static readonly TYPE = 'ContentExport';
  public static readonly NAME = '内容导出';
  public static readonly DESCRIPTION = '内容导出节点，支持多种格式的内容导出';

  constructor(nodeType: string = ContentExportNode.TYPE, name: string = ContentExportNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInputPort('export', 'trigger', '执行导出');
    this.addInputPort('content', 'object', '内容对象');
    this.addInputPort('format', 'string', '导出格式');
    this.addInputPort('outputPath', 'string', '输出路径');
    this.addInputPort('exportOptions', 'object', '导出选项');

    // 输出端口
    this.addOutputPort('exportPath', 'string', '导出路径');
    this.addOutputPort('exportSize', 'number', '导出大小');
    this.addOutputPort('exportFormat', 'string', '导出格式');
    this.addOutputPort('onExportStarted', 'trigger', '导出开始');
    this.addOutputPort('onExportCompleted', 'trigger', '导出完成');
    this.addOutputPort('onExportFailed', 'trigger', '导出失败');
  }

  public execute(inputs?: any): any {
    try {
      if (inputs?.export) {
        return this.exportContent(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('ContentExportNode', '执行失败', error);
      return {
        ...this.getDefaultOutputs(),
        onExportFailed: true
      };
    }
  }

  private exportContent(inputs: any): any {
    const content = inputs?.content;
    const format = inputs?.format as string || 'json';
    const outputPath = inputs?.outputPath as string || './export';
    const exportOptions = inputs?.exportOptions as Record<string, any> || {};

    if (!content) {
      Debug.warn('ContentExportNode', '未提供内容对象');
      return {
        ...this.getDefaultOutputs(),
        onExportFailed: true
      };
    }

    const exportPath = `${outputPath}.${format}`;
    const exportSize = this.calculateExportSize(content, format);

    Debug.log('ContentExportNode', `内容导出完成: ${exportPath}, 格式: ${format}, 大小: ${exportSize} bytes`);

    return {
      exportPath,
      exportSize,
      exportFormat: format,
      onExportStarted: false,
      onExportCompleted: true,
      onExportFailed: false
    };
  }

  private calculateExportSize(content: any, format: string): number {
    // 模拟导出大小计算
    const baseSize = JSON.stringify(content).length;
    
    switch (format) {
      case 'json':
        return baseSize;
      case 'xml':
        return Math.round(baseSize * 1.5);
      case 'binary':
        return Math.round(baseSize * 0.7);
      default:
        return baseSize;
    }
  }

  private getDefaultOutputs(): any {
    return {
      exportPath: '',
      exportSize: 0,
      exportFormat: '',
      onExportStarted: false,
      onExportCompleted: false,
      onExportFailed: false
    };
  }
}
