/**
 * 路径编辑节点实现
 * 提供路径创建、编辑、样条曲线等功能
 */

import { VisualScriptNode } from '../VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3, Vector2 } from 'three';

/**
 * 路径点接口
 */
export interface PathPoint {
  id: string;
  position: Vector3;
  tangentIn?: Vector3;
  tangentOut?: Vector3;
  type: 'linear' | 'bezier' | 'smooth';
}

/**
 * 路径配置接口
 */
export interface PathConfig {
  id: string;
  name: string;
  points: PathPoint[];
  closed: boolean;
  interpolationType: 'linear' | 'cubic' | 'catmull-rom';
  tension: number;
  resolution: number;
}

/**
 * 路径编辑器节点
 */
export class PathEditorNode extends VisualScriptNode {
  public static readonly TYPE = 'PathEditor';
  public static readonly NAME = '路径编辑器';
  public static readonly DESCRIPTION = '主路径编辑器界面，提供路径创建和编辑功能';

  private paths: Map<string, PathConfig> = new Map();
  private selectedPath: string | null = null;

  constructor(nodeType: string = PathEditorNode.TYPE, name: string = PathEditorNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInputPort('openEditor', 'trigger', '打开编辑器');
    this.addInputPort('closeEditor', 'trigger', '关闭编辑器');
    this.addInputPort('selectPath', 'trigger', '选择路径');
    this.addInputPort('pathId', 'string', '路径ID');
    this.addInputPort('editorMode', 'string', '编辑模式');

    // 输出端口
    this.addOutputPort('editorState', 'string', '编辑器状态');
    this.addOutputPort('selectedPath', 'object', '选中路径');
    this.addOutputPort('pathList', 'array', '路径列表');
    this.addOutputPort('onEditorOpened', 'trigger', '编辑器打开');
    this.addOutputPort('onEditorClosed', 'trigger', '编辑器关闭');
    this.addOutputPort('onPathSelected', 'trigger', '路径选中');
  }

  public execute(inputs?: any): any {
    try {
      if (inputs?.openEditor) {
        return this.openEditor(inputs);
      } else if (inputs?.closeEditor) {
        return this.closeEditor();
      } else if (inputs?.selectPath) {
        return this.selectPath(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('PathEditorNode', '执行失败', error);
      return this.getDefaultOutputs();
    }
  }

  private openEditor(inputs: any): any {
    const editorMode = inputs?.editorMode as string || 'edit';

    Debug.log('PathEditorNode', `路径编辑器打开: ${editorMode}模式`);

    return {
      editorState: 'open',
      selectedPath: this.selectedPath ? this.paths.get(this.selectedPath) : null,
      pathList: Array.from(this.paths.values()),
      onEditorOpened: true,
      onEditorClosed: false,
      onPathSelected: false
    };
  }

  private closeEditor(): any {
    Debug.log('PathEditorNode', '路径编辑器关闭');

    return {
      editorState: 'closed',
      selectedPath: null,
      pathList: [],
      onEditorOpened: false,
      onEditorClosed: true,
      onPathSelected: false
    };
  }

  private selectPath(inputs: any): any {
    const pathId = inputs?.pathId as string;

    if (!pathId || !this.paths.has(pathId)) {
      Debug.warn('PathEditorNode', `路径不存在: ${pathId}`);
      return this.getDefaultOutputs();
    }

    this.selectedPath = pathId;
    const selectedPathData = this.paths.get(pathId)!;

    Debug.log('PathEditorNode', `路径选中: ${pathId}`);

    return {
      editorState: 'open',
      selectedPath: selectedPathData,
      pathList: Array.from(this.paths.values()),
      onEditorOpened: false,
      onEditorClosed: false,
      onPathSelected: true
    };
  }

  private getDefaultOutputs(): any {
    return {
      editorState: 'closed',
      selectedPath: null,
      pathList: Array.from(this.paths.values()),
      onEditorOpened: false,
      onEditorClosed: false,
      onPathSelected: false
    };
  }
}

/**
 * 路径创建节点
 */
export class PathCreationNode extends VisualScriptNode {
  public static readonly TYPE = 'PathCreation';
  public static readonly NAME = '路径创建';
  public static readonly DESCRIPTION = '创建新的路径对象，支持多种路径类型';

  constructor(nodeType: string = PathCreationNode.TYPE, name: string = PathCreationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInputPort('create', 'trigger', '创建路径');
    this.addInputPort('pathName', 'string', '路径名称');
    this.addInputPort('pathType', 'string', '路径类型');
    this.addInputPort('startPoint', 'vector3', '起始点');
    this.addInputPort('endPoint', 'vector3', '结束点');
    this.addInputPort('closed', 'boolean', '闭合路径');

    // 输出端口
    this.addOutputPort('path', 'object', '路径对象');
    this.addOutputPort('pathId', 'string', '路径ID');
    this.addOutputPort('onCreated', 'trigger', '创建完成');
    this.addOutputPort('onError', 'trigger', '创建失败');
  }

  public execute(inputs?: any): any {
    try {
      if (inputs?.create) {
        return this.createPath(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('PathCreationNode', '执行失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private createPath(inputs: any): any {
    const pathName = inputs?.pathName as string || `Path_${Date.now()}`;
    const pathType = inputs?.pathType as string || 'linear';
    const startPoint = inputs?.startPoint as Vector3 || new Vector3(0, 0, 0);
    const endPoint = inputs?.endPoint as Vector3 || new Vector3(1, 0, 0);
    const closed = inputs?.closed as boolean || false;

    const pathId = this.generatePathId();
    const path: PathConfig = {
      id: pathId,
      name: pathName,
      points: [
        {
          id: 'start',
          position: startPoint,
          type: pathType as any
        },
        {
          id: 'end',
          position: endPoint,
          type: pathType as any
        }
      ],
      closed,
      interpolationType: pathType as any,
      tension: 0.5,
      resolution: 50
    };

    Debug.log('PathCreationNode', `路径创建成功: ${pathName} (${pathId})`);

    return {
      path,
      pathId,
      onCreated: true,
      onError: false
    };
  }

  private generatePathId(): string {
    return `path_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getDefaultOutputs(): any {
    return {
      path: null,
      pathId: '',
      onCreated: false,
      onError: false
    };
  }
}

/**
 * 路径点编辑器节点
 */
export class PathPointEditorNode extends VisualScriptNode {
  public static readonly TYPE = 'PathPointEditor';
  public static readonly NAME = '路径点编辑器';
  public static readonly DESCRIPTION = '编辑路径控制点，支持精确的点位调整';

  constructor(nodeType: string = PathPointEditorNode.TYPE, name: string = PathPointEditorNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInputPort('addPoint', 'trigger', '添加点');
    this.addInputPort('removePoint', 'trigger', '删除点');
    this.addInputPort('movePoint', 'trigger', '移动点');
    this.addInputPort('path', 'object', '路径对象');
    this.addInputPort('pointId', 'string', '点ID');
    this.addInputPort('position', 'vector3', '位置');
    this.addInputPort('insertIndex', 'number', '插入索引');

    // 输出端口
    this.addOutputPort('updatedPath', 'object', '更新路径');
    this.addOutputPort('pointCount', 'number', '点数量');
    this.addOutputPort('selectedPoint', 'object', '选中点');
    this.addOutputPort('onPointAdded', 'trigger', '点添加');
    this.addOutputPort('onPointRemoved', 'trigger', '点删除');
    this.addOutputPort('onPointMoved', 'trigger', '点移动');
  }

  public execute(inputs?: any): any {
    try {
      const path = inputs?.path as PathConfig;
      if (!path) {
        Debug.warn('PathPointEditorNode', '未提供路径对象');
        return this.getDefaultOutputs();
      }

      if (inputs?.addPoint) {
        return this.addPoint(path, inputs);
      } else if (inputs?.removePoint) {
        return this.removePoint(path, inputs);
      } else if (inputs?.movePoint) {
        return this.movePoint(path, inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('PathPointEditorNode', '执行失败', error);
      return this.getDefaultOutputs();
    }
  }

  private addPoint(path: PathConfig, inputs: any): any {
    const position = inputs?.position as Vector3 || new Vector3(0, 0, 0);
    const insertIndex = inputs?.insertIndex as number || path.points.length;

    const newPoint: PathPoint = {
      id: `point_${Date.now()}`,
      position,
      type: 'linear'
    };

    const updatedPath = { ...path };
    updatedPath.points = [...path.points];
    updatedPath.points.splice(insertIndex, 0, newPoint);

    Debug.log('PathPointEditorNode', `路径点添加成功: ${newPoint.id}`);

    return {
      updatedPath,
      pointCount: updatedPath.points.length,
      selectedPoint: newPoint,
      onPointAdded: true,
      onPointRemoved: false,
      onPointMoved: false
    };
  }

  private removePoint(path: PathConfig, inputs: any): any {
    const pointId = inputs?.pointId as string;

    if (!pointId) {
      Debug.warn('PathPointEditorNode', '未提供点ID');
      return this.getDefaultOutputs();
    }

    const updatedPath = { ...path };
    updatedPath.points = path.points.filter(point => point.id !== pointId);

    Debug.log('PathPointEditorNode', `路径点删除成功: ${pointId}`);

    return {
      updatedPath,
      pointCount: updatedPath.points.length,
      selectedPoint: null,
      onPointAdded: false,
      onPointRemoved: true,
      onPointMoved: false
    };
  }

  private movePoint(path: PathConfig, inputs: any): any {
    const pointId = inputs?.pointId as string;
    const position = inputs?.position as Vector3;

    if (!pointId || !position) {
      Debug.warn('PathPointEditorNode', '缺少点ID或位置');
      return this.getDefaultOutputs();
    }

    const updatedPath = { ...path };
    updatedPath.points = path.points.map(point => 
      point.id === pointId ? { ...point, position } : point
    );

    const movedPoint = updatedPath.points.find(point => point.id === pointId);

    Debug.log('PathPointEditorNode', `路径点移动成功: ${pointId}`);

    return {
      updatedPath,
      pointCount: updatedPath.points.length,
      selectedPoint: movedPoint || null,
      onPointAdded: false,
      onPointRemoved: false,
      onPointMoved: true
    };
  }

  private getDefaultOutputs(): any {
    return {
      updatedPath: null,
      pointCount: 0,
      selectedPoint: null,
      onPointAdded: false,
      onPointRemoved: false,
      onPointMoved: false
    };
  }
}
