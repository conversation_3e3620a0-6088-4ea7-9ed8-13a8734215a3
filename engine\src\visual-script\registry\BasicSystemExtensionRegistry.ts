/**
 * 注册批次5：基础系统扩展节点注册表
 * 注册50个基础系统扩展节点到编辑器
 * 
 * 节点分类：
 * - 渲染系统扩展节点：15个
 * - 物理系统扩展节点：12个
 * - 音频系统扩展节点：8个
 * - 输入系统扩展节点：15个
 * 
 * 创建时间：2025年7月8日
 * 负责团队：基础系统团队
 */

import { NodeRegistry, NodeCategory, createNodeInfo } from './NodeRegistry';
import { Debug } from '../../utils/Debug';

// 导入渲染系统扩展节点（15个）
// 高级着色器节点（5个）
import {
  AdvancedVertexShaderNode,
  AdvancedFragmentShaderNode,
  TessellationShaderNode,
  GeometryShaderNode,
  ComputeShaderNode
} from '../nodes/rendering/AdvancedShaderNodes';

// 后处理效果节点（5个）
import {
  AdvancedBloomNode,
  MotionBlurNode,
  DepthOfFieldNode,
  ScreenSpaceReflectionNode,
  VolumetricLightingNode
} from '../nodes/rendering/AdvancedPostProcessingNodes';

// 光照系统节点（3个）
import {
  GlobalIlluminationNode,
  VolumetricFogNode,
  LightProbeNode
} from '../nodes/rendering/AdvancedLightingNodes';

// 阴影系统节点（2个）
import {
  CascadedShadowMapNode,
  ContactShadowNode
} from '../nodes/rendering/AdvancedShadowNodes';

// 导入物理系统扩展节点（12个）
// 高级碰撞检测节点（4个）
import {
  ContinuousCollisionDetectionNode,
  ConvexHullCollisionNode,
  MeshCollisionNode,
  HeightFieldCollisionNode
} from '../nodes/physics/AdvancedCollisionNodes';

// 流体物理节点（3个）
import {
  FluidSimulationNode,
  ParticleFluidNode,
  FluidInteractionNode
} from '../nodes/physics/FluidPhysicsNodes';

// 软体物理节点（3个）
import {
  SoftBodyNode,
  ClothSimulationNode,
  RopeSimulationNode
} from '../nodes/physics/SoftBodyNodes';

// 约束系统节点（2个）
import {
  AdvancedConstraintNode,
  ConstraintSolverNode
} from '../nodes/physics/ConstraintNodes';

// 导入音频系统扩展节点（8个）
// 3D音频节点（3个）
import {
  SpatialAudioNode,
  AudioOcclusionNode,
  ReverbZoneNode
} from '../nodes/audio/SpatialAudioNodes';

// 音频效果节点（3个）
import {
  AudioFilterNode,
  AudioDistortionNode,
  AudioChorusNode
} from '../nodes/audio/AudioEffectNodes';

// 音频分析节点（2个）
import {
  AudioSpectrumAnalyzerNode,
  AudioBeatDetectionNode
} from '../nodes/audio/AudioAnalysisNodes';

// 导入输入系统扩展节点（15个）
// VR/AR输入节点（6个）
import {
  VRControllerInputNode,
  VRHeadsetTrackingNode,
  ARTouchInputNode,
  ARGestureInputNode,
  SpatialInputNode,
  EyeTrackingInputNode
} from '../nodes/input/VRARInputNodes';

// 手势识别节点（4个）
import {
  HandGestureRecognitionNode,
  FingerTrackingNode,
  PalmDetectionNode,
  GestureClassificationNode
} from '../nodes/input/GestureRecognitionNodes';

// 语音输入节点（3个）
import {
  SpeechRecognitionNode,
  VoiceCommandNode,
  SpeechToTextNode
} from '../nodes/input/VoiceInputNodes';

// 传感器输入节点（2个）
import {
  AccelerometerNode,
  GyroscopeNode
} from '../nodes/input/SensorInputNodes';

/**
 * 基础系统扩展节点注册表
 */
export class BasicSystemExtensionRegistry {
  private static instance: BasicSystemExtensionRegistry;
  private registered: boolean = false;

  public static getInstance(): BasicSystemExtensionRegistry {
    if (!BasicSystemExtensionRegistry.instance) {
      BasicSystemExtensionRegistry.instance = new BasicSystemExtensionRegistry();
    }
    return BasicSystemExtensionRegistry.instance;
  }

  /**
   * 注册所有基础系统扩展节点
   */
  public async registerNodes(): Promise<void> {
    if (this.registered) {
      Debug.warn('BasicSystemExtensionRegistry', '基础系统扩展节点已经注册过了');
      return;
    }

    Debug.log('BasicSystemExtensionRegistry', '开始注册基础系统扩展节点...');

    try {
      // 注册渲染系统扩展节点（15个）
      this.registerRenderingExtensionNodes();
      this.registerLightingAndShadowNodes();

      // 注册物理系统扩展节点（12个）
      this.registerPhysicsExtensionNodes();

      // 注册音频系统扩展节点（8个）
      this.registerAudioExtensionNodes();

      // 注册输入系统扩展节点（15个）
      this.registerInputExtensionNodes();

      this.registered = true;

      Debug.log('BasicSystemExtensionRegistry', '基础系统扩展节点注册完成');
      Debug.log('BasicSystemExtensionRegistry', `渲染系统扩展节点：15个`);
      Debug.log('BasicSystemExtensionRegistry', `物理系统扩展节点：12个`);
      Debug.log('BasicSystemExtensionRegistry', `音频系统扩展节点：8个`);
      Debug.log('BasicSystemExtensionRegistry', `输入系统扩展节点：15个`);
      Debug.log('BasicSystemExtensionRegistry', `总计：50个节点`);

    } catch (error) {
      Debug.error('BasicSystemExtensionRegistry', '基础系统扩展节点注册失败:', error);
      throw error;
    }
  }

  /**
   * 注册渲染系统扩展节点（15个）
   */
  private registerRenderingExtensionNodes(): void {
    Debug.log('BasicSystemExtensionRegistry', '注册渲染系统扩展节点...');

    // 注册高级着色器节点（5个）
    NodeRegistry.registerNode(createNodeInfo({
      type: 'AdvancedVertexShader',
      name: '高级顶点着色器',
      category: NodeCategory.RENDERING,
      description: '高级顶点着色器处理，支持复杂变换和动画',
      nodeClass: AdvancedVertexShaderNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'AdvancedFragmentShader',
      name: '高级片段着色器',
      category: NodeCategory.RENDERING,
      description: '高级片段着色器处理，支持复杂材质和光照',
      nodeClass: AdvancedFragmentShaderNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'TessellationShader',
      name: '细分着色器',
      category: NodeCategory.RENDERING,
      description: '几何细分着色器，提供动态网格细分',
      nodeClass: TessellationShaderNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'GeometryShader',
      name: '几何着色器',
      category: NodeCategory.RENDERING,
      description: '几何着色器处理，支持几何体生成和修改',
      nodeClass: GeometryShaderNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'ComputeShader',
      name: '计算着色器',
      category: NodeCategory.RENDERING,
      description: '通用计算着色器，支持GPU并行计算',
      nodeClass: ComputeShaderNode
    }));

    // 注册后处理效果节点（5个）
    NodeRegistry.registerNode(createNodeInfo({
      type: 'AdvancedBloom',
      name: '高级泛光效果',
      category: NodeCategory.RENDERING,
      description: '高级泛光后处理效果，支持多层泛光',
      nodeClass: AdvancedBloomNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'MotionBlur',
      name: '运动模糊',
      category: NodeCategory.RENDERING,
      description: '运动模糊后处理效果，模拟相机运动',
      nodeClass: MotionBlurNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'DepthOfField',
      name: '景深效果',
      category: NodeCategory.RENDERING,
      description: '景深后处理效果，模拟相机焦距',
      nodeClass: DepthOfFieldNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'ScreenSpaceReflection',
      name: '屏幕空间反射',
      category: NodeCategory.RENDERING,
      description: '屏幕空间反射效果，提供实时反射',
      nodeClass: ScreenSpaceReflectionNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'VolumetricLighting',
      name: '体积光照',
      category: NodeCategory.RENDERING,
      description: '体积光照效果，模拟光线散射',
      nodeClass: VolumetricLightingNode
    }));

    Debug.log('BasicSystemExtensionRegistry', '渲染系统扩展节点注册完成: 15个');
  }

  /**
   * 注册光照和阴影系统节点（5个）
   */
  private registerLightingAndShadowNodes(): void {
    // 注册光照系统节点（3个）
    NodeRegistry.registerNode(createNodeInfo({
      type: 'GlobalIllumination',
      name: '全局光照',
      category: NodeCategory.RENDERING,
      description: '全局光照系统，提供真实的光线追踪',
      nodeClass: GlobalIlluminationNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'VolumetricFog',
      name: '体积雾效',
      category: NodeCategory.RENDERING,
      description: '体积雾效果，模拟大气散射',
      nodeClass: VolumetricFogNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'LightProbe',
      name: '光照探针',
      category: NodeCategory.RENDERING,
      description: '光照探针系统，提供环境光照',
      nodeClass: LightProbeNode
    }));

    // 注册阴影系统节点（2个）
    NodeRegistry.registerNode(createNodeInfo({
      type: 'CascadedShadowMap',
      name: '级联阴影贴图',
      category: NodeCategory.RENDERING,
      description: '级联阴影贴图，提供高质量阴影',
      nodeClass: CascadedShadowMapNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'ContactShadow',
      name: '接触阴影',
      category: NodeCategory.RENDERING,
      description: '接触阴影效果，增强物体接触感',
      nodeClass: ContactShadowNode
    }));
  }

  /**
   * 注册物理系统扩展节点（12个）
   */
  private registerPhysicsExtensionNodes(): void {
    Debug.log('BasicSystemExtensionRegistry', '注册物理系统扩展节点...');

    // 注册高级碰撞检测节点（4个）
    NodeRegistry.registerNode(createNodeInfo({
      type: 'ContinuousCollisionDetection',
      name: '连续碰撞检测',
      category: NodeCategory.PHYSICS,
      description: '连续碰撞检测，防止高速物体穿透',
      nodeClass: ContinuousCollisionDetectionNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'ConvexHullCollision',
      name: '凸包碰撞',
      category: NodeCategory.PHYSICS,
      description: '凸包碰撞检测，适用于复杂几何体',
      nodeClass: ConvexHullCollisionNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'MeshCollision',
      name: '网格碰撞',
      category: NodeCategory.PHYSICS,
      description: '精确网格碰撞检测，支持复杂模型',
      nodeClass: MeshCollisionNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'HeightFieldCollision',
      name: '高度场碰撞',
      category: NodeCategory.PHYSICS,
      description: '高度场碰撞检测，适用于地形',
      nodeClass: HeightFieldCollisionNode
    }));

    // 注册流体物理节点（3个）
    NodeRegistry.registerNode(createNodeInfo({
      type: 'FluidSimulation',
      name: '流体模拟',
      category: NodeCategory.PHYSICS,
      description: '流体物理模拟，支持液体和气体',
      nodeClass: FluidSimulationNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'ParticleFluid',
      name: '粒子流体',
      category: NodeCategory.PHYSICS,
      description: '基于粒子的流体模拟系统',
      nodeClass: ParticleFluidNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'FluidInteraction',
      name: '流体交互',
      category: NodeCategory.PHYSICS,
      description: '流体与物体的交互模拟',
      nodeClass: FluidInteractionNode
    }));

    // 注册软体物理节点（3个）
    NodeRegistry.registerNode(createNodeInfo({
      type: 'SoftBody',
      name: '软体物理',
      category: NodeCategory.PHYSICS,
      description: '软体物理模拟，支持变形物体',
      nodeClass: SoftBodyNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'ClothSimulation',
      name: '布料模拟',
      category: NodeCategory.PHYSICS,
      description: '布料物理模拟，支持织物效果',
      nodeClass: ClothSimulationNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'RopeSimulation',
      name: '绳索模拟',
      category: NodeCategory.PHYSICS,
      description: '绳索物理模拟，支持柔性连接',
      nodeClass: RopeSimulationNode
    }));

    // 注册约束系统节点（2个）
    NodeRegistry.registerNode(createNodeInfo({
      type: 'AdvancedConstraint',
      name: '高级约束',
      category: NodeCategory.PHYSICS,
      description: '高级物理约束系统，支持复杂约束',
      nodeClass: AdvancedConstraintNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'ConstraintSolver',
      name: '约束求解器',
      category: NodeCategory.PHYSICS,
      description: '约束求解器，处理复杂约束关系',
      nodeClass: ConstraintSolverNode
    }));

    Debug.log('BasicSystemExtensionRegistry', '物理系统扩展节点注册完成: 12个');
  }

  /**
   * 注册音频系统扩展节点（8个）
   */
  private registerAudioExtensionNodes(): void {
    Debug.log('BasicSystemExtensionRegistry', '注册音频系统扩展节点...');

    // 注册3D音频节点（3个）
    NodeRegistry.registerNode(createNodeInfo({
      type: 'SpatialAudio',
      name: '空间音频',
      category: NodeCategory.AUDIO,
      description: '3D空间音频系统，提供立体声定位',
      nodeClass: SpatialAudioNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'AudioOcclusion',
      name: '音频遮挡',
      category: NodeCategory.AUDIO,
      description: '音频遮挡效果，模拟声音传播阻挡',
      nodeClass: AudioOcclusionNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'ReverbZone',
      name: '混响区域',
      category: NodeCategory.AUDIO,
      description: '混响区域效果，模拟环境声学',
      nodeClass: ReverbZoneNode
    }));

    // 注册音频效果节点（3个）
    NodeRegistry.registerNode(createNodeInfo({
      type: 'AudioFilter',
      name: '音频滤波器',
      category: NodeCategory.AUDIO,
      description: '音频滤波处理，支持多种滤波类型',
      nodeClass: AudioFilterNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'AudioDistortion',
      name: '音频失真',
      category: NodeCategory.AUDIO,
      description: '音频失真效果，提供音色变化',
      nodeClass: AudioDistortionNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'AudioChorus',
      name: '音频合唱',
      category: NodeCategory.AUDIO,
      description: '音频合唱效果，增强音频层次',
      nodeClass: AudioChorusNode
    }));

    // 注册音频分析节点（2个）
    NodeRegistry.registerNode(createNodeInfo({
      type: 'AudioSpectrumAnalyzer',
      name: '音频频谱分析',
      category: NodeCategory.AUDIO,
      description: '音频频谱分析器，提供频域信息',
      nodeClass: AudioSpectrumAnalyzerNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'AudioBeatDetection',
      name: '音频节拍检测',
      category: NodeCategory.AUDIO,
      description: '音频节拍检测，识别音乐节奏',
      nodeClass: AudioBeatDetectionNode
    }));

    Debug.log('BasicSystemExtensionRegistry', '音频系统扩展节点注册完成: 8个');
  }

  /**
   * 注册输入系统扩展节点（15个）
   */
  private registerInputExtensionNodes(): void {
    Debug.log('BasicSystemExtensionRegistry', '注册输入系统扩展节点...');

    // 注册VR/AR输入节点（6个）
    NodeRegistry.registerNode(createNodeInfo({
      type: 'VRControllerInput',
      name: 'VR控制器输入',
      category: NodeCategory.INPUT,
      description: 'VR控制器输入处理，支持位置和按键',
      nodeClass: VRControllerInputNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'VRHeadsetTracking',
      name: 'VR头显追踪',
      category: NodeCategory.INPUT,
      description: 'VR头显位置追踪，提供头部运动数据',
      nodeClass: VRHeadsetTrackingNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'ARTouchInput',
      name: 'AR触摸输入',
      category: NodeCategory.INPUT,
      description: 'AR触摸输入处理，支持屏幕交互',
      nodeClass: ARTouchInputNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'ARGestureInput',
      name: 'AR手势输入',
      category: NodeCategory.INPUT,
      description: 'AR手势输入识别，支持空中手势',
      nodeClass: ARGestureInputNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'SpatialInput',
      name: '空间输入',
      category: NodeCategory.INPUT,
      description: '空间输入处理，支持3D空间交互',
      nodeClass: SpatialInputNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'EyeTrackingInput',
      name: '眼动追踪输入',
      category: NodeCategory.INPUT,
      description: '眼动追踪输入，提供视线方向数据',
      nodeClass: EyeTrackingInputNode
    }));

    // 注册手势识别节点（4个）
    NodeRegistry.registerNode(createNodeInfo({
      type: 'HandGestureRecognition',
      name: '手势识别',
      category: NodeCategory.INPUT,
      description: '手势识别系统，识别手部动作',
      nodeClass: HandGestureRecognitionNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'FingerTracking',
      name: '手指追踪',
      category: NodeCategory.INPUT,
      description: '手指追踪系统，提供精确手指位置',
      nodeClass: FingerTrackingNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'PalmDetection',
      name: '手掌检测',
      category: NodeCategory.INPUT,
      description: '手掌检测系统，识别手掌位置',
      nodeClass: PalmDetectionNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'GestureClassification',
      name: '手势分类',
      category: NodeCategory.INPUT,
      description: '手势分类系统，识别特定手势',
      nodeClass: GestureClassificationNode
    }));

    // 注册语音输入节点（3个）
    NodeRegistry.registerNode(createNodeInfo({
      type: 'SpeechRecognition',
      name: '语音识别',
      category: NodeCategory.INPUT,
      description: '语音识别系统，转换语音为文本',
      nodeClass: SpeechRecognitionNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'VoiceCommand',
      name: '语音命令',
      category: NodeCategory.INPUT,
      description: '语音命令系统，执行语音指令',
      nodeClass: VoiceCommandNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'SpeechToText',
      name: '语音转文本',
      category: NodeCategory.INPUT,
      description: '语音转文本系统，实时语音转换',
      nodeClass: SpeechToTextNode
    }));

    // 注册传感器输入节点（2个）
    NodeRegistry.registerNode(createNodeInfo({
      type: 'Accelerometer',
      name: '加速度计',
      category: NodeCategory.INPUT,
      description: '加速度计传感器，检测设备加速度',
      nodeClass: AccelerometerNode
    }));

    NodeRegistry.registerNode(createNodeInfo({
      type: 'Gyroscope',
      name: '陀螺仪',
      category: NodeCategory.INPUT,
      description: '陀螺仪传感器，检测设备旋转',
      nodeClass: GyroscopeNode
    }));

    Debug.log('BasicSystemExtensionRegistry', '输入系统扩展节点注册完成: 15个');
  }

  /**
   * 检查注册状态
   */
  public isRegistered(): boolean {
    return this.registered;
  }

  /**
   * 获取所有已注册的节点类型
   */
  public getAllRegisteredNodeTypes(): string[] {
    return [
      // 渲染系统扩展节点（15个）
      'AdvancedVertexShader', 'AdvancedFragmentShader', 'TessellationShader', 'GeometryShader', 'ComputeShader',
      'AdvancedBloom', 'MotionBlur', 'DepthOfField', 'ScreenSpaceReflection', 'VolumetricLighting',
      'GlobalIllumination', 'VolumetricFog', 'LightProbe', 'CascadedShadowMap', 'ContactShadow',

      // 物理系统扩展节点（12个）
      'ContinuousCollisionDetection', 'ConvexHullCollision', 'MeshCollision', 'HeightFieldCollision',
      'FluidSimulation', 'ParticleFluid', 'FluidInteraction',
      'SoftBody', 'ClothSimulation', 'RopeSimulation',
      'AdvancedConstraint', 'ConstraintSolver',

      // 音频系统扩展节点（8个）
      'SpatialAudio', 'AudioOcclusion', 'ReverbZone',
      'AudioFilter', 'AudioDistortion', 'AudioChorus',
      'AudioSpectrumAnalyzer', 'AudioBeatDetection',

      // 输入系统扩展节点（15个）
      'VRControllerInput', 'VRHeadsetTracking', 'ARTouchInput', 'ARGestureInput', 'SpatialInput', 'EyeTrackingInput',
      'HandGestureRecognition', 'FingerTracking', 'PalmDetection', 'GestureClassification',
      'SpeechRecognition', 'VoiceCommand', 'SpeechToText',
      'Accelerometer', 'Gyroscope'
    ];
  }

  /**
   * 获取节点统计信息
   */
  public getNodeStatistics(): { [category: string]: number } {
    return {
      '渲染系统扩展': 15,
      '物理系统扩展': 12,
      '音频系统扩展': 8,
      '输入系统扩展': 15,
      '总计': 50
    };
  }
}
