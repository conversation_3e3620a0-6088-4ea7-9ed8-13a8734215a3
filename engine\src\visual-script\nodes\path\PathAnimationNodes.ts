/**
 * 路径动画节点实现
 * 提供路径跟随、动画控制、事件触发等功能
 */

import { VisualScriptNode } from '../VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3, Object3D } from 'three';
import { PathConfig } from './PathEditingNodes';

/**
 * 路径跟随配置接口
 */
export interface PathFollowConfig {
  target: Object3D;
  path: PathConfig;
  speed: number;
  progress: number;
  loop: boolean;
  lookAhead: boolean;
  upVector: Vector3;
}

/**
 * 路径跟随节点
 */
export class PathFollowingNode extends VisualScriptNode {
  public static readonly TYPE = 'PathFollowing';
  public static readonly NAME = '路径跟随';
  public static readonly DESCRIPTION = '对象沿路径移动，支持速度和方向控制';

  private followConfigs: Map<string, PathFollowConfig> = new Map();

  constructor(nodeType: string = PathFollowingNode.TYPE, name: string = PathFollowingNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInputPort('startFollow', 'trigger', '开始跟随');
    this.addInputPort('stopFollow', 'trigger', '停止跟随');
    this.addInputPort('pauseFollow', 'trigger', '暂停跟随');
    this.addInputPort('target', 'object', '目标对象');
    this.addInputPort('path', 'object', '路径对象');
    this.addInputPort('speed', 'number', '移动速度');
    this.addInputPort('loop', 'boolean', '循环跟随');
    this.addInputPort('lookAhead', 'boolean', '朝向前方');

    // 输出端口
    this.addOutputPort('currentPosition', 'vector3', '当前位置');
    this.addOutputPort('progress', 'number', '跟随进度');
    this.addOutputPort('isFollowing', 'boolean', '是否跟随中');
    this.addOutputPort('onStarted', 'trigger', '开始跟随');
    this.addOutputPort('onStopped', 'trigger', '停止跟随');
    this.addOutputPort('onCompleted', 'trigger', '跟随完成');
  }

  public execute(inputs?: any): any {
    try {
      if (inputs?.startFollow) {
        return this.startFollow(inputs);
      } else if (inputs?.stopFollow) {
        return this.stopFollow(inputs);
      } else if (inputs?.pauseFollow) {
        return this.pauseFollow(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('PathFollowingNode', '执行失败', error);
      return this.getDefaultOutputs();
    }
  }

  private startFollow(inputs: any): any {
    const target = inputs?.target as Object3D;
    const path = inputs?.path as PathConfig;
    const speed = inputs?.speed as number || 1.0;
    const loop = inputs?.loop as boolean || false;
    const lookAhead = inputs?.lookAhead as boolean || true;

    if (!target || !path) {
      Debug.warn('PathFollowingNode', '缺少目标对象或路径');
      return this.getDefaultOutputs();
    }

    const followConfig: PathFollowConfig = {
      target,
      path,
      speed: Math.max(0, speed),
      progress: 0,
      loop,
      lookAhead,
      upVector: new Vector3(0, 1, 0)
    };

    const configId = this.generateConfigId();
    this.followConfigs.set(configId, followConfig);

    Debug.log('PathFollowingNode', `路径跟随开始: ${target.name || 'unnamed'}`);

    return {
      currentPosition: target.position.clone(),
      progress: 0,
      isFollowing: true,
      onStarted: true,
      onStopped: false,
      onCompleted: false
    };
  }

  private stopFollow(inputs: any): any {
    const target = inputs?.target as Object3D;

    if (!target) {
      Debug.warn('PathFollowingNode', '未指定目标对象');
      return this.getDefaultOutputs();
    }

    // 查找并移除配置
    for (const [configId, config] of this.followConfigs.entries()) {
      if (config.target === target) {
        this.followConfigs.delete(configId);
        break;
      }
    }

    Debug.log('PathFollowingNode', `路径跟随停止: ${target.name || 'unnamed'}`);

    return {
      currentPosition: target.position.clone(),
      progress: 0,
      isFollowing: false,
      onStarted: false,
      onStopped: true,
      onCompleted: false
    };
  }

  private pauseFollow(inputs: any): any {
    Debug.log('PathFollowingNode', '路径跟随暂停');

    return {
      currentPosition: new Vector3(),
      progress: 0,
      isFollowing: false,
      onStarted: false,
      onStopped: false,
      onCompleted: false
    };
  }

  private generateConfigId(): string {
    return `follow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getDefaultOutputs(): any {
    return {
      currentPosition: new Vector3(),
      progress: 0,
      isFollowing: false,
      onStarted: false,
      onStopped: false,
      onCompleted: false
    };
  }
}

/**
 * 数字人路径跟随节点
 */
export class AvatarPathFollowingNode extends VisualScriptNode {
  public static readonly TYPE = 'AvatarPathFollowing';
  public static readonly NAME = '数字人路径跟随';
  public static readonly DESCRIPTION = '数字人沿路径移动，支持智能导航';

  constructor(nodeType: string = AvatarPathFollowingNode.TYPE, name: string = AvatarPathFollowingNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInputPort('startNavigation', 'trigger', '开始导航');
    this.addInputPort('stopNavigation', 'trigger', '停止导航');
    this.addInputPort('avatar', 'object', '数字人对象');
    this.addInputPort('path', 'object', '路径对象');
    this.addInputPort('walkSpeed', 'number', '行走速度');
    this.addInputPort('avoidObstacles', 'boolean', '避障');

    // 输出端口
    this.addOutputPort('avatarPosition', 'vector3', '数字人位置');
    this.addOutputPort('walkingState', 'string', '行走状态');
    this.addOutputPort('navigationProgress', 'number', '导航进度');
    this.addOutputPort('onNavigationStarted', 'trigger', '导航开始');
    this.addOutputPort('onNavigationCompleted', 'trigger', '导航完成');
    this.addOutputPort('onObstacleDetected', 'trigger', '检测到障碍');
  }

  public execute(inputs?: any): any {
    try {
      if (inputs?.startNavigation) {
        return this.startNavigation(inputs);
      } else if (inputs?.stopNavigation) {
        return this.stopNavigation(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('AvatarPathFollowingNode', '执行失败', error);
      return this.getDefaultOutputs();
    }
  }

  private startNavigation(inputs: any): any {
    const avatar = inputs?.avatar as Object3D;
    const path = inputs?.path as PathConfig;
    const walkSpeed = inputs?.walkSpeed as number || 1.5;
    const avoidObstacles = inputs?.avoidObstacles as boolean || true;

    if (!avatar || !path) {
      Debug.warn('AvatarPathFollowingNode', '缺少数字人对象或路径');
      return this.getDefaultOutputs();
    }

    Debug.log('AvatarPathFollowingNode', `数字人导航开始: ${avatar.name || 'unnamed'}`);

    return {
      avatarPosition: avatar.position.clone(),
      walkingState: 'walking',
      navigationProgress: 0,
      onNavigationStarted: true,
      onNavigationCompleted: false,
      onObstacleDetected: false
    };
  }

  private stopNavigation(inputs: any): any {
    const avatar = inputs?.avatar as Object3D;

    Debug.log('AvatarPathFollowingNode', `数字人导航停止: ${avatar?.name || 'unnamed'}`);

    return {
      avatarPosition: avatar?.position.clone() || new Vector3(),
      walkingState: 'idle',
      navigationProgress: 0,
      onNavigationStarted: false,
      onNavigationCompleted: false,
      onObstacleDetected: false
    };
  }

  private getDefaultOutputs(): any {
    return {
      avatarPosition: new Vector3(),
      walkingState: 'idle',
      navigationProgress: 0,
      onNavigationStarted: false,
      onNavigationCompleted: false,
      onObstacleDetected: false
    };
  }
}

/**
 * 路径动画节点
 */
export class PathAnimationNode extends VisualScriptNode {
  public static readonly TYPE = 'PathAnimation';
  public static readonly NAME = '路径动画';
  public static readonly DESCRIPTION = '路径动画控制，支持复杂动画序列';

  constructor(nodeType: string = PathAnimationNode.TYPE, name: string = PathAnimationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInputPort('playAnimation', 'trigger', '播放动画');
    this.addInputPort('pauseAnimation', 'trigger', '暂停动画');
    this.addInputPort('stopAnimation', 'trigger', '停止动画');
    this.addInputPort('path', 'object', '路径对象');
    this.addInputPort('duration', 'number', '动画时长');
    this.addInputPort('easing', 'string', '缓动函数');

    // 输出端口
    this.addOutputPort('animationState', 'string', '动画状态');
    this.addOutputPort('currentTime', 'number', '当前时间');
    this.addOutputPort('animationProgress', 'number', '动画进度');
    this.addOutputPort('onAnimationStarted', 'trigger', '动画开始');
    this.addOutputPort('onAnimationCompleted', 'trigger', '动画完成');
  }

  public execute(inputs?: any): any {
    try {
      if (inputs?.playAnimation) {
        return this.playAnimation(inputs);
      } else if (inputs?.pauseAnimation) {
        return this.pauseAnimation();
      } else if (inputs?.stopAnimation) {
        return this.stopAnimation();
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('PathAnimationNode', '执行失败', error);
      return this.getDefaultOutputs();
    }
  }

  private playAnimation(inputs: any): any {
    const path = inputs?.path as PathConfig;
    const duration = inputs?.duration as number || 5.0;
    const easing = inputs?.easing as string || 'linear';

    if (!path) {
      Debug.warn('PathAnimationNode', '未提供路径对象');
      return this.getDefaultOutputs();
    }

    Debug.log('PathAnimationNode', `路径动画开始: ${duration}秒, ${easing}缓动`);

    return {
      animationState: 'playing',
      currentTime: 0,
      animationProgress: 0,
      onAnimationStarted: true,
      onAnimationCompleted: false
    };
  }

  private pauseAnimation(): any {
    Debug.log('PathAnimationNode', '路径动画暂停');

    return {
      animationState: 'paused',
      currentTime: 0,
      animationProgress: 0,
      onAnimationStarted: false,
      onAnimationCompleted: false
    };
  }

  private stopAnimation(): any {
    Debug.log('PathAnimationNode', '路径动画停止');

    return {
      animationState: 'stopped',
      currentTime: 0,
      animationProgress: 0,
      onAnimationStarted: false,
      onAnimationCompleted: false
    };
  }

  private getDefaultOutputs(): any {
    return {
      animationState: 'idle',
      currentTime: 0,
      animationProgress: 0,
      onAnimationStarted: false,
      onAnimationCompleted: false
    };
  }
}

/**
 * 路径事件触发器节点
 */
export class PathEventTriggerNode extends VisualScriptNode {
  public static readonly TYPE = 'PathEventTrigger';
  public static readonly NAME = '路径事件触发器';
  public static readonly DESCRIPTION = '路径事件处理，支持位置触发事件';

  private triggers: Map<string, { position: number; event: string }> = new Map();

  constructor(nodeType: string = PathEventTriggerNode.TYPE, name: string = PathEventTriggerNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInputPort('addTrigger', 'trigger', '添加触发器');
    this.addInputPort('removeTrigger', 'trigger', '移除触发器');
    this.addInputPort('checkTriggers', 'trigger', '检查触发器');
    this.addInputPort('triggerPosition', 'number', '触发位置');
    this.addInputPort('eventName', 'string', '事件名称');
    this.addInputPort('currentProgress', 'number', '当前进度');

    // 输出端口
    this.addOutputPort('triggeredEvent', 'string', '触发事件');
    this.addOutputPort('triggerCount', 'number', '触发器数量');
    this.addOutputPort('onEventTriggered', 'trigger', '事件触发');
    this.addOutputPort('onTriggerAdded', 'trigger', '触发器添加');
    this.addOutputPort('onTriggerRemoved', 'trigger', '触发器移除');
  }

  public execute(inputs?: any): any {
    try {
      if (inputs?.addTrigger) {
        return this.addTrigger(inputs);
      } else if (inputs?.removeTrigger) {
        return this.removeTrigger(inputs);
      } else if (inputs?.checkTriggers) {
        return this.checkTriggers(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('PathEventTriggerNode', '执行失败', error);
      return this.getDefaultOutputs();
    }
  }

  private addTrigger(inputs: any): any {
    const triggerPosition = inputs?.triggerPosition as number;
    const eventName = inputs?.eventName as string;

    if (triggerPosition === undefined || !eventName) {
      Debug.warn('PathEventTriggerNode', '缺少触发位置或事件名称');
      return this.getDefaultOutputs();
    }

    const triggerId = `trigger_${Date.now()}`;
    this.triggers.set(triggerId, {
      position: Math.max(0, Math.min(1, triggerPosition)),
      event: eventName
    });

    Debug.log('PathEventTriggerNode', `触发器添加: ${eventName} @ ${triggerPosition}`);

    return {
      triggeredEvent: '',
      triggerCount: this.triggers.size,
      onEventTriggered: false,
      onTriggerAdded: true,
      onTriggerRemoved: false
    };
  }

  private removeTrigger(inputs: any): any {
    const eventName = inputs?.eventName as string;

    if (!eventName) {
      Debug.warn('PathEventTriggerNode', '未指定事件名称');
      return this.getDefaultOutputs();
    }

    // 移除匹配的触发器
    for (const [triggerId, trigger] of this.triggers.entries()) {
      if (trigger.event === eventName) {
        this.triggers.delete(triggerId);
        break;
      }
    }

    Debug.log('PathEventTriggerNode', `触发器移除: ${eventName}`);

    return {
      triggeredEvent: '',
      triggerCount: this.triggers.size,
      onEventTriggered: false,
      onTriggerAdded: false,
      onTriggerRemoved: true
    };
  }

  private checkTriggers(inputs: any): any {
    const currentProgress = inputs?.currentProgress as number;

    if (currentProgress === undefined) {
      Debug.warn('PathEventTriggerNode', '未提供当前进度');
      return this.getDefaultOutputs();
    }

    // 检查是否有触发器被激活
    for (const [triggerId, trigger] of this.triggers.entries()) {
      if (Math.abs(currentProgress - trigger.position) < 0.01) {
        Debug.log('PathEventTriggerNode', `事件触发: ${trigger.event}`);
        
        return {
          triggeredEvent: trigger.event,
          triggerCount: this.triggers.size,
          onEventTriggered: true,
          onTriggerAdded: false,
          onTriggerRemoved: false
        };
      }
    }

    return {
      triggeredEvent: '',
      triggerCount: this.triggers.size,
      onEventTriggered: false,
      onTriggerAdded: false,
      onTriggerRemoved: false
    };
  }

  private getDefaultOutputs(): any {
    return {
      triggeredEvent: '',
      triggerCount: this.triggers.size,
      onEventTriggered: false,
      onTriggerAdded: false,
      onTriggerRemoved: false
    };
  }
}

/**
 * 路径循环控制节点
 */
export class PathLoopControlNode extends VisualScriptNode {
  public static readonly TYPE = 'PathLoopControl';
  public static readonly NAME = '路径循环控制';
  public static readonly DESCRIPTION = '控制路径循环模式，支持多种循环方式';

  constructor(nodeType: string = PathLoopControlNode.TYPE, name: string = PathLoopControlNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInputPort('setLoopMode', 'trigger', '设置循环模式');
    this.addInputPort('loopMode', 'string', '循环模式');
    this.addInputPort('loopCount', 'number', '循环次数');
    this.addInputPort('pingPong', 'boolean', '往返循环');

    // 输出端口
    this.addOutputPort('currentLoopMode', 'string', '当前循环模式');
    this.addOutputPort('remainingLoops', 'number', '剩余循环');
    this.addOutputPort('onLoopModeChanged', 'trigger', '循环模式改变');
  }

  public execute(inputs?: any): any {
    try {
      if (inputs?.setLoopMode) {
        return this.setLoopMode(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('PathLoopControlNode', '执行失败', error);
      return this.getDefaultOutputs();
    }
  }

  private setLoopMode(inputs: any): any {
    const loopMode = inputs?.loopMode as string || 'none';
    const loopCount = inputs?.loopCount as number || -1;
    const pingPong = inputs?.pingPong as boolean || false;

    Debug.log('PathLoopControlNode', `循环模式设置: ${loopMode}, 次数: ${loopCount}, 往返: ${pingPong}`);

    return {
      currentLoopMode: loopMode,
      remainingLoops: loopCount,
      onLoopModeChanged: true
    };
  }

  private getDefaultOutputs(): any {
    return {
      currentLoopMode: 'none',
      remainingLoops: 0,
      onLoopModeChanged: false
    };
  }
}

/**
 * 路径速度控制节点
 */
export class PathSpeedControlNode extends VisualScriptNode {
  public static readonly TYPE = 'PathSpeedControl';
  public static readonly NAME = '路径速度控制';
  public static readonly DESCRIPTION = '控制路径移动速度，支持变速和缓动';

  constructor(nodeType: string = PathSpeedControlNode.TYPE, name: string = PathSpeedControlNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInputPort('setSpeed', 'trigger', '设置速度');
    this.addInputPort('speed', 'number', '移动速度');
    this.addInputPort('acceleration', 'number', '加速度');
    this.addInputPort('maxSpeed', 'number', '最大速度');
    this.addInputPort('easingType', 'string', '缓动类型');

    // 输出端口
    this.addOutputPort('currentSpeed', 'number', '当前速度');
    this.addOutputPort('targetSpeed', 'number', '目标速度');
    this.addOutputPort('onSpeedChanged', 'trigger', '速度改变');
  }

  public execute(inputs?: any): any {
    try {
      if (inputs?.setSpeed) {
        return this.setSpeed(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('PathSpeedControlNode', '执行失败', error);
      return this.getDefaultOutputs();
    }
  }

  private setSpeed(inputs: any): any {
    const speed = inputs?.speed as number || 1.0;
    const acceleration = inputs?.acceleration as number || 1.0;
    const maxSpeed = inputs?.maxSpeed as number || 10.0;
    const easingType = inputs?.easingType as string || 'linear';

    const clampedSpeed = Math.max(0, Math.min(maxSpeed, speed));

    Debug.log('PathSpeedControlNode', `速度设置: ${clampedSpeed}, 缓动: ${easingType}`);

    return {
      currentSpeed: clampedSpeed,
      targetSpeed: clampedSpeed,
      onSpeedChanged: true
    };
  }

  private getDefaultOutputs(): any {
    return {
      currentSpeed: 0,
      targetSpeed: 0,
      onSpeedChanged: false
    };
  }
}
