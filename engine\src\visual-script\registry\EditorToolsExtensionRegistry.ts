/**
 * 编辑器工具扩展节点注册表
 * 批次6：编辑器工具扩展（32个节点）
 * 注册编辑器界面节点、资源管理节点、调试工具节点到系统
 */

import { NodeRegistry, createNodeInfo, NodeCategory } from './NodeRegistry';
import { Debug } from '../../utils/Debug';

// 导入编辑器界面节点
import {
  ToolbarManagerNode,
  ToolbarButtonNode,
  ToolbarGroupNode,
  ToolbarSeparatorNode,
  PanelManagerNode,
  PanelContentNode,
  PanelTabNode,
  PanelLayoutNode,
  MenuSystemNode,
  MenuItemNode,
  StatusBarNode,
  StatusBarItemNode
} from '../nodes/editor/EditorToolsExtensionNodes';

// 导入资源管理节点
import {
  ResourceImporterNode,
  ResourceBatchImporterNode,
  ResourceFormatConverterNode,
  ResourceBatchConverterNode,
  ResourcePreviewerNode,
  ResourceValidatorNode,
  ResourceOptimizerNode
} from '../nodes/editor/EditorToolsExtensionNodes';

import {
  ResourceCacheManagerNode,
  ResourceCacheOperationNode,
  ResourceCacheStatsNode
} from '../nodes/editor/EditorToolsExtensionNodes2';

// 导入调试工具节点
import {
  PerformanceProfilerNode,
  MemoryProfilerNode,
  CPUProfilerNode,
  RenderProfilerNode,
  DebugInfoDisplayNode,
  DebugConsoleNode,
  DebugBreakpointNode,
  LogSystemManagerNode,
  LogFilterNode,
  LogExportNode
} from '../nodes/editor/EditorToolsExtensionNodes3';

/**
 * 编辑器工具扩展节点注册表类
 */
export class EditorToolsExtensionRegistry {
  private static instance: EditorToolsExtensionRegistry;
  private nodeRegistry: NodeRegistry;
  private registered = false;

  private constructor() {
    this.nodeRegistry = NodeRegistry.getInstance();
  }

  public static getInstance(): EditorToolsExtensionRegistry {
    if (!EditorToolsExtensionRegistry.instance) {
      EditorToolsExtensionRegistry.instance = new EditorToolsExtensionRegistry();
    }
    return EditorToolsExtensionRegistry.instance;
  }

  /**
   * 注册所有编辑器工具扩展节点
   */
  public registerAllNodes(): void {
    if (this.registered) {
      console.log('编辑器工具扩展节点已经注册过了');
      return;
    }

    console.log('开始注册编辑器工具扩展节点...');

    // 注册编辑器界面节点（12个）
    this.registerEditorInterfaceNodes();

    // 注册资源管理节点（10个）
    this.registerResourceManagementNodes();

    // 注册调试工具节点（10个）
    this.registerDebugToolNodes();

    this.registered = true;
    console.log('编辑器工具扩展节点注册完成！总计32个节点');
  }

  /**
   * 注册编辑器界面节点（12个）
   */
  private registerEditorInterfaceNodes(): void {
    Debug.log('EditorToolsExtensionRegistry', '注册编辑器界面节点...');

    // 工具栏管理器节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/toolbarManager',
      name: '工具栏管理器',
      description: '管理编辑器工具栏的显示、隐藏和自定义',
      category: NodeCategory.EDITOR,
      nodeClass: ToolbarManagerNode,
      color: '#607D8B',
      icon: 'toolbar'
    }));

    // 工具栏按钮节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/toolbarButton',
      name: '工具栏按钮',
      description: '创建和管理工具栏按钮',
      category: NodeCategory.EDITOR,
      nodeClass: ToolbarButtonNode,
      color: '#607D8B',
      icon: 'button'
    }));

    // 工具栏分组节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/toolbarGroup',
      name: '工具栏分组',
      description: '创建工具栏按钮分组',
      category: NodeCategory.EDITOR,
      nodeClass: ToolbarGroupNode,
      color: '#607D8B',
      icon: 'group'
    }));

    // 工具栏分隔符节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/toolbarSeparator',
      name: '工具栏分隔符',
      description: '在工具栏中添加分隔符',
      category: NodeCategory.EDITOR,
      nodeClass: ToolbarSeparatorNode,
      color: '#607D8B',
      icon: 'separator'
    }));

    // 面板管理器节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/panelManager',
      name: '面板管理器',
      description: '管理编辑器面板的显示、隐藏和布局',
      category: NodeCategory.EDITOR,
      nodeClass: PanelManagerNode,
      color: '#546E7A',
      icon: 'panel'
    }));

    // 面板内容节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/panelContent',
      name: '面板内容',
      description: '定义面板的内容和组件',
      category: NodeCategory.EDITOR,
      nodeClass: PanelContentNode,
      color: '#546E7A',
      icon: 'content'
    }));

    // 面板标签页节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/panelTab',
      name: '面板标签页',
      description: '创建面板标签页',
      category: NodeCategory.EDITOR,
      nodeClass: PanelTabNode,
      color: '#546E7A',
      icon: 'tab'
    }));

    // 面板布局节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/panelLayout',
      name: '面板布局',
      description: '管理面板的布局和排列',
      category: NodeCategory.EDITOR,
      nodeClass: PanelLayoutNode,
      color: '#546E7A',
      icon: 'layout'
    }));

    // 菜单系统节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/menuSystem',
      name: '菜单系统',
      description: '管理编辑器菜单系统',
      category: NodeCategory.EDITOR,
      nodeClass: MenuSystemNode,
      color: '#455A64',
      icon: 'menu'
    }));

    // 菜单项节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/menuItem',
      name: '菜单项',
      description: '创建菜单项',
      category: NodeCategory.EDITOR,
      nodeClass: MenuItemNode,
      color: '#455A64',
      icon: 'menu_item'
    }));

    // 状态栏节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/statusBar',
      name: '状态栏',
      description: '管理编辑器状态栏',
      category: NodeCategory.EDITOR,
      nodeClass: StatusBarNode,
      color: '#37474F',
      icon: 'status_bar'
    }));

    // 状态栏项节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/statusBarItem',
      name: '状态栏项',
      description: '创建状态栏项',
      category: NodeCategory.EDITOR,
      nodeClass: StatusBarItemNode,
      color: '#37474F',
      icon: 'status_item'
    }));

    Debug.log('EditorToolsExtensionRegistry', '编辑器界面节点注册完成: 12个节点');
  }

  /**
   * 注册资源管理节点（10个）
   */
  private registerResourceManagementNodes(): void {
    Debug.log('EditorToolsExtensionRegistry', '注册资源管理节点...');

    // 资源导入器节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/resourceImporter',
      name: '资源导入器',
      description: '导入外部资源文件',
      category: NodeCategory.EDITOR,
      nodeClass: ResourceImporterNode,
      color: '#8BC34A',
      icon: 'import'
    }));

    // 资源批量导入节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/resourceBatchImporter',
      name: '资源批量导入器',
      description: '批量导入多个资源文件',
      category: NodeCategory.EDITOR,
      nodeClass: ResourceBatchImporterNode,
      color: '#8BC34A',
      icon: 'batch_import'
    }));

    // 资源格式转换节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/resourceFormatConverter',
      name: '资源格式转换器',
      description: '转换资源文件格式',
      category: NodeCategory.EDITOR,
      nodeClass: ResourceFormatConverterNode,
      color: '#689F38',
      icon: 'convert'
    }));

    // 资源批量转换节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/resourceBatchConverter',
      name: '资源批量转换器',
      description: '批量转换多个资源文件格式',
      category: NodeCategory.EDITOR,
      nodeClass: ResourceBatchConverterNode,
      color: '#689F38',
      icon: 'batch_convert'
    }));

    // 资源缓存管理器节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/resourceCacheManager',
      name: '资源缓存管理器',
      description: '管理资源缓存系统',
      category: NodeCategory.EDITOR,
      nodeClass: ResourceCacheManagerNode,
      color: '#558B2F',
      icon: 'cache'
    }));

    // 资源缓存操作节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/resourceCacheOperation',
      name: '资源缓存操作',
      description: '执行资源缓存操作',
      category: NodeCategory.EDITOR,
      nodeClass: ResourceCacheOperationNode,
      color: '#558B2F',
      icon: 'cache_operation'
    }));

    // 资源预览器节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/resourcePreviewer',
      name: '资源预览器',
      description: '预览资源文件内容',
      category: NodeCategory.EDITOR,
      nodeClass: ResourcePreviewerNode,
      color: '#689F38',
      icon: 'preview'
    }));

    // 资源验证器节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/resourceValidator',
      name: '资源验证器',
      description: '验证资源文件的完整性和有效性',
      category: NodeCategory.EDITOR,
      nodeClass: ResourceValidatorNode,
      color: '#689F38',
      icon: 'validate'
    }));

    // 资源优化器节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/resourceOptimizer',
      name: '资源优化器',
      description: '优化资源文件以提高性能',
      category: NodeCategory.EDITOR,
      nodeClass: ResourceOptimizerNode,
      color: '#689F38',
      icon: 'optimize'
    }));

    // 资源缓存统计节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/resourceCacheStats',
      name: '资源缓存统计',
      description: '获取资源缓存统计信息',
      category: NodeCategory.EDITOR,
      nodeClass: ResourceCacheStatsNode,
      color: '#558B2F',
      icon: 'cache_stats'
    }));

    Debug.log('EditorToolsExtensionRegistry', '资源管理节点注册完成: 10个节点');
  }

  /**
   * 注册调试工具节点（10个）
   */
  private registerDebugToolNodes(): void {
    Debug.log('EditorToolsExtensionRegistry', '注册调试工具节点...');

    // 性能分析器节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/performanceProfiler',
      name: '性能分析器',
      description: '分析编辑器性能指标',
      category: NodeCategory.EDITOR,
      nodeClass: PerformanceProfilerNode,
      color: '#FF9800',
      icon: 'performance'
    }));

    // 内存分析器节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/memoryProfiler',
      name: '内存分析器',
      description: '分析内存使用情况',
      category: NodeCategory.EDITOR,
      nodeClass: MemoryProfilerNode,
      color: '#FF9800',
      icon: 'memory'
    }));

    // CPU分析器节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/cpuProfiler',
      name: 'CPU分析器',
      description: '分析CPU使用情况',
      category: NodeCategory.EDITOR,
      nodeClass: CPUProfilerNode,
      color: '#FF9800',
      icon: 'cpu'
    }));

    // 渲染分析器节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/renderProfiler',
      name: '渲染分析器',
      description: '分析渲染性能',
      category: NodeCategory.EDITOR,
      nodeClass: RenderProfilerNode,
      color: '#FF9800',
      icon: 'render'
    }));

    // 调试信息显示节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/debugInfoDisplay',
      name: '调试信息显示',
      description: '显示调试信息面板',
      category: NodeCategory.EDITOR,
      nodeClass: DebugInfoDisplayNode,
      color: '#F57C00',
      icon: 'debug_info'
    }));

    // 调试控制台节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/debugConsole',
      name: '调试控制台',
      description: '管理调试控制台',
      category: NodeCategory.EDITOR,
      nodeClass: DebugConsoleNode,
      color: '#F57C00',
      icon: 'console'
    }));

    // 调试断点管理节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/debugBreakpoint',
      name: '调试断点管理',
      description: '管理调试断点',
      category: NodeCategory.EDITOR,
      nodeClass: DebugBreakpointNode,
      color: '#F57C00',
      icon: 'breakpoint'
    }));

    // 日志系统管理节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/logSystemManager',
      name: '日志系统管理器',
      description: '管理编辑器日志系统',
      category: NodeCategory.EDITOR,
      nodeClass: LogSystemManagerNode,
      color: '#E65100',
      icon: 'log_system'
    }));

    // 日志过滤器节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/logFilter',
      name: '日志过滤器',
      description: '过滤和搜索日志消息',
      category: NodeCategory.EDITOR,
      nodeClass: LogFilterNode,
      color: '#E65100',
      icon: 'log_filter'
    }));

    // 日志导出节点
    this.nodeRegistry.registerNode(createNodeInfo({
      type: 'editor/logExport',
      name: '日志导出器',
      description: '导出日志到文件',
      category: NodeCategory.EDITOR,
      nodeClass: LogExportNode,
      color: '#E65100',
      icon: 'log_export'
    }));

    Debug.log('EditorToolsExtensionRegistry', '调试工具节点注册完成: 10个节点');
  }

  /**
   * 获取所有已注册的节点类型
   */
  public getAllRegisteredNodeTypes(): string[] {
    return [
      // 编辑器界面节点
      'editor/toolbarManager',
      'editor/toolbarButton',
      'editor/toolbarGroup',
      'editor/toolbarSeparator',
      'editor/panelManager',
      'editor/panelContent',
      'editor/panelTab',
      'editor/panelLayout',
      'editor/menuSystem',
      'editor/menuItem',
      'editor/statusBar',
      'editor/statusBarItem',

      // 资源管理节点
      'editor/resourceImporter',
      'editor/resourceBatchImporter',
      'editor/resourceFormatConverter',
      'editor/resourceBatchConverter',
      'editor/resourceCacheManager',
      'editor/resourceCacheOperation',
      'editor/resourceCacheStats',

      // 调试工具节点
      'editor/performanceProfiler',
      'editor/memoryProfiler',
      'editor/cpuProfiler',
      'editor/renderProfiler',
      'editor/debugInfoDisplay',
      'editor/debugConsole',
      'editor/debugBreakpoint',
      'editor/logSystemManager',
      'editor/logFilter',
      'editor/logExport'
    ];
  }

  /**
   * 检查节点是否已注册
   */
  public isRegistered(): boolean {
    return this.registered;
  }

  /**
   * 重置注册状态（用于测试）
   */
  public resetRegistration(): void {
    this.registered = false;
  }

  /**
   * 获取注册统计信息
   */
  public getRegistrationStats(): any {
    return {
      totalNodes: 32,
      interfaceNodes: 12,
      resourceNodes: 10,
      debugNodes: 10,
      registered: this.registered,
      registrationTime: new Date().toISOString()
    };
  }
}

// 导出单例实例
export const editorToolsExtensionRegistry = EditorToolsExtensionRegistry.getInstance();
