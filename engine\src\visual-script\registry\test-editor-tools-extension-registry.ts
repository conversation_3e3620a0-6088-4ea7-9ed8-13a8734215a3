/**
 * 编辑器工具扩展节点注册表测试
 * 验证批次6：编辑器工具扩展（32个节点）的注册
 */

import { editorToolsExtensionRegistry } from './EditorToolsExtensionRegistry';
import { NodeRegistry } from './NodeRegistry';

/**
 * 测试编辑器工具扩展节点注册
 */
function testEditorToolsExtensionRegistry(): void {
  console.log('=== 编辑器工具扩展节点注册表测试 ===');

  try {
    // 重置注册状态
    editorToolsExtensionRegistry.resetRegistration();
    
    // 执行注册
    console.log('开始注册编辑器工具扩展节点...');
    editorToolsExtensionRegistry.registerAllNodes();
    
    // 验证注册状态
    const isRegistered = editorToolsExtensionRegistry.isRegistered();
    console.log(`注册状态: ${isRegistered ? '✅ 已注册' : '❌ 未注册'}`);
    
    // 获取注册统计
    const stats = editorToolsExtensionRegistry.getRegistrationStats();
    console.log('注册统计:', stats);
    
    // 验证所有节点类型
    const nodeTypes = editorToolsExtensionRegistry.getAllRegisteredNodeTypes();
    console.log(`注册的节点类型数量: ${nodeTypes.length}`);
    
    // 验证每个节点类型是否在NodeRegistry中
    const nodeRegistry = NodeRegistry.getInstance();
    let registeredCount = 0;
    let failedNodes: string[] = [];
    
    console.log('\n验证节点注册情况:');
    nodeTypes.forEach(nodeType => {
      const nodeInfo = nodeRegistry.getNodeInfo(nodeType);
      if (nodeInfo) {
        registeredCount++;
        console.log(`✅ ${nodeType}: ${nodeInfo.name}`);
      } else {
        failedNodes.push(nodeType);
        console.log(`❌ ${nodeType}: 未找到`);
      }
    });
    
    console.log(`\n注册成功: ${registeredCount}/${nodeTypes.length}`);
    
    if (failedNodes.length > 0) {
      console.log('注册失败的节点:', failedNodes);
    }
    
    // 测试节点创建
    console.log('\n测试节点创建:');
    testNodeCreation(nodeTypes.slice(0, 5)); // 测试前5个节点
    
    // 验证节点分类
    console.log('\n验证节点分类:');
    testNodeCategories();
    
    console.log('\n=== 编辑器工具扩展节点注册表测试完成 ===');
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

/**
 * 测试节点创建
 */
function testNodeCreation(nodeTypes: string[]): void {
  const nodeRegistry = NodeRegistry.getInstance();
  
  nodeTypes.forEach(nodeType => {
    try {
      const node = nodeRegistry.createNode(nodeType);
      if (node) {
        console.log(`✅ 创建节点成功: ${nodeType} (${node.name})`);
        
        // 测试节点执行
        try {
          const result = node.execute({});
          console.log(`  ✅ 节点执行成功: ${Object.keys(result || {}).length}个输出`);
        } catch (execError) {
          console.log(`  ⚠️ 节点执行失败: ${execError}`);
        }
      } else {
        console.log(`❌ 创建节点失败: ${nodeType}`);
      }
    } catch (error) {
      console.log(`❌ 创建节点异常: ${nodeType} - ${error}`);
    }
  });
}

/**
 * 测试节点分类
 */
function testNodeCategories(): void {
  const nodeRegistry = NodeRegistry.getInstance();
  const nodeTypes = editorToolsExtensionRegistry.getAllRegisteredNodeTypes();
  
  const categories = new Map<string, number>();
  
  nodeTypes.forEach(nodeType => {
    const nodeInfo = nodeRegistry.getNodeInfo(nodeType);
    if (nodeInfo) {
      const category = nodeInfo.category;
      categories.set(category, (categories.get(category) || 0) + 1);
    }
  });
  
  console.log('节点分类统计:');
  categories.forEach((count, category) => {
    console.log(`  ${category}: ${count}个节点`);
  });
}

/**
 * 测试特定节点功能
 */
function testSpecificNodes(): void {
  console.log('\n=== 测试特定节点功能 ===');
  
  const nodeRegistry = NodeRegistry.getInstance();
  
  // 测试工具栏管理器节点
  console.log('\n测试工具栏管理器节点:');
  const toolbarManager = nodeRegistry.createNode('editor/toolbarManager');
  if (toolbarManager) {
    const result = toolbarManager.execute({
      visible: true,
      position: 'top',
      customButtons: [
        { id: 'btn1', label: '按钮1', icon: 'icon1' },
        { id: 'btn2', label: '按钮2', icon: 'icon2' }
      ]
    });
    console.log('工具栏管理器执行结果:', result);
  }
  
  // 测试资源导入器节点
  console.log('\n测试资源导入器节点:');
  const resourceImporter = nodeRegistry.createNode('editor/resourceImporter');
  if (resourceImporter) {
    const result = resourceImporter.execute({
      filePath: '/path/to/texture.png',
      importType: 'texture',
      targetFolder: 'textures'
    });
    console.log('资源导入器执行结果:', result);
  }
  
  // 测试性能分析器节点
  console.log('\n测试性能分析器节点:');
  const performanceProfiler = nodeRegistry.createNode('editor/performanceProfiler');
  if (performanceProfiler) {
    const result = performanceProfiler.execute({
      profilingEnabled: true,
      sampleInterval: 1000,
      metrics: ['fps', 'memory', 'cpu']
    });
    console.log('性能分析器执行结果:', result);
  }
}

/**
 * 运行所有测试
 */
function runAllTests(): void {
  testEditorToolsExtensionRegistry();
  testSpecificNodes();
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllTests();
}

export {
  testEditorToolsExtensionRegistry,
  testNodeCreation,
  testNodeCategories,
  testSpecificNodes,
  runAllTests
};
