# 编辑器工具扩展节点注册表

## 概述

编辑器工具扩展节点注册表负责注册批次6的32个编辑器工具扩展节点，包括编辑器界面节点、资源管理节点和调试工具节点。

## 节点分类

### 批次6.1: 编辑器界面节点（12个节点）

#### 工具栏节点（4个）
- **ToolbarManagerNode**: 工具栏管理器 - 管理编辑器工具栏的显示、隐藏和自定义
- **ToolbarButtonNode**: 工具栏按钮 - 创建和管理工具栏按钮
- **ToolbarGroupNode**: 工具栏分组 - 创建工具栏按钮分组
- **ToolbarSeparatorNode**: 工具栏分隔符 - 在工具栏中添加分隔符

#### 面板管理节点（4个）
- **PanelManagerNode**: 面板管理器 - 管理编辑器面板的显示、隐藏和布局
- **PanelContentNode**: 面板内容 - 定义面板的内容和组件
- **PanelTabNode**: 面板标签页 - 创建面板标签页
- **PanelLayoutNode**: 面板布局 - 管理面板的布局和排列

#### 菜单系统节点（2个）
- **MenuSystemNode**: 菜单系统 - 管理编辑器菜单系统
- **MenuItemNode**: 菜单项 - 创建菜单项

#### 状态栏节点（2个）
- **StatusBarNode**: 状态栏 - 管理编辑器状态栏
- **StatusBarItemNode**: 状态栏项 - 创建状态栏项

### 批次6.2: 资源管理节点（10个节点）

#### 资源导入节点（4个）
- **ResourceImporterNode**: 资源导入器 - 导入外部资源文件
- **ResourceBatchImporterNode**: 资源批量导入器 - 批量导入多个资源文件
- **ResourceFormatConverterNode**: 资源格式转换器 - 转换资源文件格式
- **ResourceBatchConverterNode**: 资源批量转换器 - 批量转换多个资源文件格式

#### 资源转换节点（3个）
- **ResourceFormatConverterNode**: 资源格式转换器 - 转换资源文件格式
- **ResourceBatchConverterNode**: 资源批量转换器 - 批量转换多个资源文件格式

#### 资源缓存节点（3个）
- **ResourceCacheManagerNode**: 资源缓存管理器 - 管理资源缓存系统
- **ResourceCacheOperationNode**: 资源缓存操作 - 执行资源缓存操作
- **ResourceCacheStatsNode**: 资源缓存统计 - 获取资源缓存统计信息

### 批次6.3: 调试工具节点（10个节点）

#### 性能分析节点（4个）
- **PerformanceProfilerNode**: 性能分析器 - 分析编辑器性能指标
- **MemoryProfilerNode**: 内存分析器 - 分析内存使用情况
- **CPUProfilerNode**: CPU分析器 - 分析CPU使用情况
- **RenderProfilerNode**: 渲染分析器 - 分析渲染性能

#### 调试信息节点（3个）
- **DebugInfoDisplayNode**: 调试信息显示 - 显示调试信息面板
- **DebugConsoleNode**: 调试控制台 - 管理调试控制台
- **DebugBreakpointNode**: 调试断点管理 - 管理调试断点

#### 日志系统节点（3个）
- **LogSystemManagerNode**: 日志系统管理器 - 管理编辑器日志系统
- **LogFilterNode**: 日志过滤器 - 过滤和搜索日志消息
- **LogExportNode**: 日志导出器 - 导出日志到文件

## 使用方法

### 注册节点

```typescript
import { editorToolsExtensionRegistry } from './EditorToolsExtensionRegistry';

// 注册所有编辑器工具扩展节点
editorToolsExtensionRegistry.registerAllNodes();
```

### 创建节点

```typescript
import { NodeRegistry } from './NodeRegistry';

const nodeRegistry = NodeRegistry.getInstance();

// 创建工具栏管理器节点
const toolbarManager = nodeRegistry.createNode('editor/toolbarManager');

// 创建资源导入器节点
const resourceImporter = nodeRegistry.createNode('editor/resourceImporter');

// 创建性能分析器节点
const performanceProfiler = nodeRegistry.createNode('editor/performanceProfiler');
```

### 使用节点

```typescript
// 使用工具栏管理器
const toolbarResult = toolbarManager.execute({
  visible: true,
  position: 'top',
  customButtons: [
    { id: 'save', label: '保存', icon: 'save' },
    { id: 'load', label: '加载', icon: 'load' }
  ]
});

// 使用资源导入器
const importResult = resourceImporter.execute({
  filePath: '/path/to/model.fbx',
  importType: 'model',
  targetFolder: 'models'
});

// 使用性能分析器
const profileResult = performanceProfiler.execute({
  profilingEnabled: true,
  sampleInterval: 1000,
  metrics: ['fps', 'memory', 'cpu']
});
```

## 节点特性

### 输入输出端口
每个节点都定义了明确的输入和输出端口，支持类型检查和数据流控制。

### 错误处理
所有节点都包含完善的错误处理机制，确保系统稳定性。

### 调试支持
节点执行过程中会输出详细的调试信息，便于开发和调试。

### 配置灵活性
节点支持丰富的配置选项，可以适应不同的使用场景。

## 测试

运行测试以验证节点注册和功能：

```bash
# 运行注册表测试
npm run test:editor-tools-extension-registry

# 或者直接运行测试文件
node test-editor-tools-extension-registry.ts
```

## 文件结构

```
editor/
├── EditorToolsExtensionNodes.ts      # 编辑器界面节点实现
├── EditorToolsExtensionNodes2.ts     # 资源管理和部分调试节点实现
├── EditorToolsExtensionNodes3.ts     # 剩余调试工具节点实现
└── index.ts                          # 模块导出

registry/
├── EditorToolsExtensionRegistry.ts   # 注册表实现
├── test-editor-tools-extension-registry.ts  # 测试文件
└── README_EditorToolsExtension.md    # 文档
```

## 注意事项

1. **依赖关系**: 确保在注册节点之前已经初始化了NodeRegistry
2. **节点ID**: 每个节点类型都有唯一的ID，遵循`editor/`前缀命名规范
3. **颜色编码**: 不同类型的节点使用不同的颜色进行区分
4. **性能考虑**: 调试工具节点在生产环境中应谨慎使用

## 更新日志

- **2025-07-08**: 完成批次6编辑器工具扩展节点的注册（32个节点）
- 包含编辑器界面节点（12个）、资源管理节点（10个）、调试工具节点（10个）
- 所有节点均已实现并通过测试验证
