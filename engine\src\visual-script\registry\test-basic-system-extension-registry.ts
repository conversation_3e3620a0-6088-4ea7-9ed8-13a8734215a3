/**
 * 批次5：基础系统扩展节点注册表测试
 * 验证50个基础系统扩展节点的注册和功能
 */

import { BasicSystemExtensionRegistry } from './BasicSystemExtensionRegistry';
import { NodeRegistry } from './NodeRegistry';

/**
 * 测试基础系统扩展节点注册
 */
async function testBasicSystemExtensionRegistry(): Promise<void> {
  console.log('🧪 开始测试批次5：基础系统扩展节点注册表...');
  console.log('='.repeat(60));

  try {
    // 1. 获取注册表实例
    const registry = BasicSystemExtensionRegistry.getInstance();
    console.log('✅ 成功获取BasicSystemExtensionRegistry实例');

    // 2. 执行节点注册
    console.log('\n📋 开始注册基础系统扩展节点...');
    await registry.registerNodes();
    console.log('✅ 基础系统扩展节点注册完成');

    // 3. 验证注册状态
    const isRegistered = registry.isRegistered();
    console.log(`\n📊 注册状态: ${isRegistered ? '✅ 已注册' : '❌ 未注册'}`);

    if (!isRegistered) {
      throw new Error('节点注册失败');
    }

    // 4. 获取节点统计信息
    console.log('\n📈 节点统计信息:');
    const stats = registry.getNodeStatistics();
    for (const [category, count] of Object.entries(stats)) {
      console.log(`  - ${category}: ${count}个`);
    }

    // 5. 获取所有已注册的节点类型
    console.log('\n📝 已注册的节点类型:');
    const nodeTypes = registry.getAllRegisteredNodeTypes();
    console.log(`总计: ${nodeTypes.length}个节点`);

    // 按分类显示节点
    console.log('\n🎯 渲染系统扩展节点 (15个):');
    const renderingNodes = nodeTypes.slice(0, 15);
    renderingNodes.forEach((nodeType, index) => {
      console.log(`  ${index + 1}. ${nodeType}`);
    });

    console.log('\n⚡ 物理系统扩展节点 (12个):');
    const physicsNodes = nodeTypes.slice(15, 27);
    physicsNodes.forEach((nodeType, index) => {
      console.log(`  ${index + 1}. ${nodeType}`);
    });

    console.log('\n🔊 音频系统扩展节点 (8个):');
    const audioNodes = nodeTypes.slice(27, 35);
    audioNodes.forEach((nodeType, index) => {
      console.log(`  ${index + 1}. ${nodeType}`);
    });

    console.log('\n🎮 输入系统扩展节点 (15个):');
    const inputNodes = nodeTypes.slice(35, 50);
    inputNodes.forEach((nodeType, index) => {
      console.log(`  ${index + 1}. ${nodeType}`);
    });

    // 6. 验证节点数量
    console.log('\n🔍 验证节点数量:');
    const expectedTotal = 50;
    const actualTotal = nodeTypes.length;
    
    if (actualTotal === expectedTotal) {
      console.log(`✅ 节点数量验证通过: ${actualTotal}/${expectedTotal}`);
    } else {
      console.log(`❌ 节点数量验证失败: ${actualTotal}/${expectedTotal}`);
      throw new Error(`节点数量不匹配: 期望${expectedTotal}个，实际${actualTotal}个`);
    }

    // 7. 测试节点注册表集成
    console.log('\n🔗 测试与主节点注册表的集成:');
    const mainRegistry = NodeRegistry;
    
    // 检查几个关键节点是否已注册到主注册表
    const testNodes = [
      'AdvancedVertexShader',
      'FluidSimulation', 
      'SpatialAudio',
      'VRControllerInput'
    ];

    for (const nodeType of testNodes) {
      const nodeInfo = mainRegistry.getNode(nodeType);
      if (nodeInfo) {
        console.log(`  ✅ ${nodeType} - 已注册到主注册表`);
      } else {
        console.log(`  ❌ ${nodeType} - 未在主注册表中找到`);
      }
    }

    console.log('\n🎉 批次5：基础系统扩展节点注册表测试完成！');
    console.log('='.repeat(60));

  } catch (error) {
    console.error('\n❌ 测试失败:', error);
    throw error;
  }
}

/**
 * 运行测试
 */
if (require.main === module) {
  testBasicSystemExtensionRegistry()
    .then(() => {
      console.log('\n✅ 所有测试通过');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 测试失败:', error);
      process.exit(1);
    });
}

export { testBasicSystemExtensionRegistry };
