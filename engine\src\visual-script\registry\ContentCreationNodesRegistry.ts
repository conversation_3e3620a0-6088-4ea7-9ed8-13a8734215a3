/**
 * 注册批次4：内容创作工具节点注册表
 * 注册41个内容创作工具节点到编辑器
 * 包括：动画编辑节点(7个) + 路径编辑节点(18个) + 水体系统节点(2个) + 材质编辑节点(10个) + 其他创作工具节点(4个)
 */

import { NodeRegistry, NodeCategory, createNodeInfo } from './NodeRegistry';
import { Debug } from '../../utils/Debug';

// 导入动画编辑节点（7个剩余）
import {
  AnimationTimelineNode,
  KeyframeEditorNode,
  AnimationCurveNode
} from '../nodes/animation/AnimationEditingNodes';

import {
  AnimationLayerManagerNode,
  AnimationBlendingNode,
  AnimationPreviewNode,
  AnimationExportNode
} from '../nodes/animation/AnimationEditingNodes2';

// 导入路径编辑节点（18个）
import {
  PathEditorNode,
  PathCreationNode,
  PathPointEditorNode,
  SplineEditorNode,
  BezierCurveEditorNode,
  PathInterpolationNode
} from '../nodes/path/PathEditingNodes';

import {
  PathValidationNode,
  PathOptimizationNode,
  PathPreviewNode,
  PathExportNode,
  PathImportNode,
  PathLibraryNode
} from '../nodes/path/PathEditingNodes2';

import {
  PathFollowingNode,
  AvatarPathFollowingNode,
  PathAnimationNode,
  PathEventTriggerNode,
  PathLoopControlNode,
  PathSpeedControlNode
} from '../nodes/path/PathAnimationNodes';

// 导入水体系统节点（2个）
import {
  WaterSimulationNode,
  FluidDynamicsNode
} from '../nodes/water/WaterSystemNodes';

// 导入材质编辑节点（10个）
import {
  MaterialEditorNode,
  TextureBlendingNode,
  MaterialLibraryNode,
  PBRMaterialNode,
  MaterialPreviewNode
} from '../nodes/material/MaterialEditingNodes';

import {
  MaterialOptimizationNode,
  MaterialVariantNode,
  MaterialAnimationNode,
  MaterialParameterNode,
  MaterialTemplateNode
} from '../nodes/material/MaterialEditingNodes2';

// 导入其他创作工具节点（4个）
import {
  AssetBrowserNode,
  ContentValidationNode,
  AssetOptimizationNode,
  ContentExportNode
} from '../nodes/content/ContentCreationNodes';

/**
 * 内容创作工具节点注册表类
 */
export class ContentCreationNodesRegistry {
  private static instance: ContentCreationNodesRegistry;
  private registry: NodeRegistry;
  private registeredNodeCount: number = 0;
  private registrationErrors: string[] = [];

  private constructor() {
    this.registry = NodeRegistry.getInstance();
  }

  public static getInstance(): ContentCreationNodesRegistry {
    if (!ContentCreationNodesRegistry.instance) {
      ContentCreationNodesRegistry.instance = new ContentCreationNodesRegistry();
    }
    return ContentCreationNodesRegistry.instance;
  }

  /**
   * 注册所有内容创作工具节点
   */
  public async registerAllNodes(): Promise<void> {
    Debug.log('ContentCreationNodesRegistry', '开始注册批次4：内容创作工具节点...');

    try {
      // 注册动画编辑节点（7个）
      await this.registerAnimationEditingNodes();
      
      // 注册路径编辑节点（18个）
      await this.registerPathEditingNodes();
      
      // 注册水体系统节点（2个）
      await this.registerWaterSystemNodes();
      
      // 注册材质编辑节点（10个）
      await this.registerMaterialEditingNodes();
      
      // 注册其他创作工具节点（4个）
      await this.registerOtherCreationNodes();

      Debug.log('ContentCreationNodesRegistry', `批次4注册完成: ${this.registeredNodeCount}个节点`);
      
      if (this.registrationErrors.length > 0) {
        Debug.warn('ContentCreationNodesRegistry', `注册过程中发现 ${this.registrationErrors.length} 个错误:`, this.registrationErrors);
      }

    } catch (error) {
      Debug.error('ContentCreationNodesRegistry', '批次4节点注册失败:', error);
      throw error;
    }
  }

  /**
   * 注册动画编辑节点（7个剩余）
   */
  private async registerAnimationEditingNodes(): Promise<void> {
    try {
      const animationNodes = [
        createNodeInfo(
          'AnimationTimeline',
          '动画时间轴',
          '动画时间轴编辑器，提供时间控制和关键帧管理功能',
          NodeCategory.ANIMATION,
          AnimationTimelineNode,
          {
            icon: 'timeline',
            color: '#9B59B6',
            tags: ['animation', 'timeline', 'keyframe', 'editing']
          }
        ),
        createNodeInfo(
          'KeyframeEditor',
          '关键帧编辑器',
          '关键帧编辑器，提供关键帧的创建、编辑、删除功能',
          NodeCategory.ANIMATION,
          KeyframeEditorNode,
          {
            icon: 'edit',
            color: '#9B59B6',
            tags: ['animation', 'keyframe', 'editing']
          }
        ),
        createNodeInfo(
          'AnimationCurve',
          '动画曲线编辑器',
          '动画曲线编辑器，提供贝塞尔曲线和缓动函数编辑',
          NodeCategory.ANIMATION,
          AnimationCurveNode,
          {
            icon: 'curve',
            color: '#9B59B6',
            tags: ['animation', 'curve', 'bezier', 'easing']
          }
        ),
        createNodeInfo(
          'AnimationLayerManager',
          '动画层管理器',
          '动画层管理器，支持多层动画混合和权重控制',
          NodeCategory.ANIMATION,
          AnimationLayerManagerNode,
          {
            icon: 'layers',
            color: '#9B59B6',
            tags: ['animation', 'layer', 'blending']
          }
        ),
        createNodeInfo(
          'AnimationBlending',
          '动画混合',
          '动画混合节点，支持多个动画的权重混合',
          NodeCategory.ANIMATION,
          AnimationBlendingNode,
          {
            icon: 'blend',
            color: '#9B59B6',
            tags: ['animation', 'blending', 'weight']
          }
        ),
        createNodeInfo(
          'AnimationPreview',
          '动画预览',
          '动画预览节点，实时预览动画效果',
          NodeCategory.ANIMATION,
          AnimationPreviewNode,
          {
            icon: 'preview',
            color: '#9B59B6',
            tags: ['animation', 'preview', 'realtime']
          }
        ),
        createNodeInfo(
          'AnimationExport',
          '动画导出',
          '动画导出节点，支持多种格式的动画导出',
          NodeCategory.ANIMATION,
          AnimationExportNode,
          {
            icon: 'export',
            color: '#9B59B6',
            tags: ['animation', 'export', 'format']
          }
        )
      ];

      // 注册节点
      for (const nodeInfo of animationNodes) {
        this.registry.registerNode(nodeInfo);
        this.registeredNodeCount++;
      }

      Debug.log('ContentCreationNodesRegistry', `动画编辑节点注册完成: ${animationNodes.length}个节点`);

    } catch (error) {
      const errorMsg = `动画编辑节点注册失败: ${error}`;
      this.registrationErrors.push(errorMsg);
      Debug.error('ContentCreationNodesRegistry', errorMsg);
    }
  }

  /**
   * 注册路径编辑节点（18个）
   */
  private async registerPathEditingNodes(): Promise<void> {
    try {
      // 路径创建与编辑节点（12个）
      const pathEditingNodes = [
        createNodeInfo(
          'PathEditor',
          '路径编辑器',
          '主路径编辑器界面，提供路径创建和编辑功能',
          NodeCategory.SCENE_MANAGEMENT,
          PathEditorNode,
          {
            icon: 'path',
            color: '#3498DB',
            tags: ['path', 'editor', 'spline', 'curve']
          }
        ),
        createNodeInfo(
          'PathCreation',
          '路径创建',
          '创建新的路径对象，支持多种路径类型',
          NodeCategory.SCENE_MANAGEMENT,
          PathCreationNode,
          {
            icon: 'add-path',
            color: '#3498DB',
            tags: ['path', 'create', 'new']
          }
        ),
        createNodeInfo(
          'PathPointEditor',
          '路径点编辑器',
          '编辑路径控制点，支持精确的点位调整',
          NodeCategory.SCENE_MANAGEMENT,
          PathPointEditorNode,
          {
            icon: 'edit-points',
            color: '#3498DB',
            tags: ['path', 'points', 'control', 'edit']
          }
        ),
        createNodeInfo(
          'SplineEditor',
          '样条曲线编辑器',
          '编辑样条曲线路径，支持平滑曲线生成',
          NodeCategory.SCENE_MANAGEMENT,
          SplineEditorNode,
          {
            icon: 'spline',
            color: '#3498DB',
            tags: ['spline', 'curve', 'smooth', 'interpolation']
          }
        ),
        createNodeInfo(
          'BezierCurveEditor',
          '贝塞尔曲线编辑器',
          '编辑贝塞尔曲线，支持精确的曲线控制',
          NodeCategory.SCENE_MANAGEMENT,
          BezierCurveEditorNode,
          {
            icon: 'bezier',
            color: '#3498DB',
            tags: ['bezier', 'curve', 'control', 'tangent']
          }
        ),
        createNodeInfo(
          'PathInterpolation',
          '路径插值',
          '设置路径插值方式，控制路径平滑度',
          NodeCategory.SCENE_MANAGEMENT,
          PathInterpolationNode,
          {
            icon: 'interpolation',
            color: '#3498DB',
            tags: ['path', 'interpolation', 'smooth', 'linear']
          }
        ),
        createNodeInfo(
          'PathValidation',
          '路径验证',
          '验证路径有效性，检查路径完整性',
          NodeCategory.SCENE_MANAGEMENT,
          PathValidationNode,
          {
            icon: 'validate',
            color: '#3498DB',
            tags: ['path', 'validation', 'check', 'integrity']
          }
        ),
        createNodeInfo(
          'PathOptimization',
          '路径优化',
          '优化路径性能，减少控制点数量',
          NodeCategory.SCENE_MANAGEMENT,
          PathOptimizationNode,
          {
            icon: 'optimize',
            color: '#3498DB',
            tags: ['path', 'optimization', 'performance', 'simplify']
          }
        ),
        createNodeInfo(
          'PathPreview',
          '路径预览',
          '实时预览路径效果，支持动画预览',
          NodeCategory.SCENE_MANAGEMENT,
          PathPreviewNode,
          {
            icon: 'preview',
            color: '#3498DB',
            tags: ['path', 'preview', 'realtime', 'animation']
          }
        ),
        createNodeInfo(
          'PathExport',
          '路径导出',
          '导出路径数据，支持多种格式',
          NodeCategory.SCENE_MANAGEMENT,
          PathExportNode,
          {
            icon: 'export',
            color: '#3498DB',
            tags: ['path', 'export', 'data', 'format']
          }
        ),
        createNodeInfo(
          'PathImport',
          '路径导入',
          '导入路径数据，支持多种格式解析',
          NodeCategory.SCENE_MANAGEMENT,
          PathImportNode,
          {
            icon: 'import',
            color: '#3498DB',
            tags: ['path', 'import', 'data', 'parse']
          }
        ),
        createNodeInfo(
          'PathLibrary',
          '路径库',
          '管理路径资源库，提供路径模板',
          NodeCategory.SCENE_MANAGEMENT,
          PathLibraryNode,
          {
            icon: 'library',
            color: '#3498DB',
            tags: ['path', 'library', 'template', 'resource']
          }
        )
      ];

      // 路径跟随与动画节点（6个）
      const pathAnimationNodes = [
        createNodeInfo(
          'PathFollowing',
          '路径跟随',
          '对象沿路径移动，支持速度和方向控制',
          NodeCategory.ANIMATION,
          PathFollowingNode,
          {
            icon: 'follow-path',
            color: '#E74C3C',
            tags: ['path', 'follow', 'movement', 'animation']
          }
        ),
        createNodeInfo(
          'AvatarPathFollowing',
          '数字人路径跟随',
          '数字人沿路径移动，支持智能导航',
          NodeCategory.ANIMATION,
          AvatarPathFollowingNode,
          {
            icon: 'avatar-path',
            color: '#E74C3C',
            tags: ['avatar', 'path', 'follow', 'navigation']
          }
        ),
        createNodeInfo(
          'PathAnimation',
          '路径动画',
          '路径动画控制，支持复杂动画序列',
          NodeCategory.ANIMATION,
          PathAnimationNode,
          {
            icon: 'path-animation',
            color: '#E74C3C',
            tags: ['path', 'animation', 'sequence', 'control']
          }
        ),
        createNodeInfo(
          'PathEventTrigger',
          '路径事件触发器',
          '路径事件处理，支持位置触发事件',
          NodeCategory.ANIMATION,
          PathEventTriggerNode,
          {
            icon: 'event-trigger',
            color: '#E74C3C',
            tags: ['path', 'event', 'trigger', 'position']
          }
        ),
        createNodeInfo(
          'PathLoopControl',
          '路径循环控制',
          '控制路径循环模式，支持多种循环方式',
          NodeCategory.ANIMATION,
          PathLoopControlNode,
          {
            icon: 'loop',
            color: '#E74C3C',
            tags: ['path', 'loop', 'cycle', 'repeat']
          }
        ),
        createNodeInfo(
          'PathSpeedControl',
          '路径速度控制',
          '控制路径移动速度，支持变速和缓动',
          NodeCategory.ANIMATION,
          PathSpeedControlNode,
          {
            icon: 'speed',
            color: '#E74C3C',
            tags: ['path', 'speed', 'velocity', 'easing']
          }
        )
      ];

      // 注册所有路径节点
      const allPathNodes = [...pathEditingNodes, ...pathAnimationNodes];
      for (const nodeInfo of allPathNodes) {
        this.registry.registerNode(nodeInfo);
        this.registeredNodeCount++;
      }

      Debug.log('ContentCreationNodesRegistry', `路径编辑节点注册完成: ${allPathNodes.length}个节点`);

    } catch (error) {
      const errorMsg = `路径编辑节点注册失败: ${error}`;
      this.registrationErrors.push(errorMsg);
      Debug.error('ContentCreationNodesRegistry', errorMsg);
    }
  }

  /**
   * 注册水体系统节点（2个）
   */
  private async registerWaterSystemNodes(): Promise<void> {
    try {
      const waterNodes = [
        createNodeInfo(
          'WaterSimulation',
          '水体模拟',
          '高级水体模拟节点，支持流体动力学和波浪效果',
          NodeCategory.PHYSICS,
          WaterSimulationNode,
          {
            icon: 'water',
            color: '#3498DB',
            tags: ['water', 'simulation', 'fluid', 'physics']
          }
        ),
        createNodeInfo(
          'FluidDynamics',
          '流体动力学',
          '流体动力学节点，提供高精度流体模拟',
          NodeCategory.PHYSICS,
          FluidDynamicsNode,
          {
            icon: 'fluid',
            color: '#3498DB',
            tags: ['fluid', 'dynamics', 'simulation', 'physics']
          }
        )
      ];

      // 注册节点
      for (const nodeInfo of waterNodes) {
        this.registry.registerNode(nodeInfo);
        this.registeredNodeCount++;
      }

      Debug.log('ContentCreationNodesRegistry', `水体系统节点注册完成: ${waterNodes.length}个节点`);

    } catch (error) {
      const errorMsg = `水体系统节点注册失败: ${error}`;
      this.registrationErrors.push(errorMsg);
      Debug.error('ContentCreationNodesRegistry', errorMsg);
    }
  }

  /**
   * 注册材质编辑节点（10个）
   */
  private async registerMaterialEditingNodes(): Promise<void> {
    try {
      const materialNodes = [
        createNodeInfo(
          'MaterialEditor',
          '材质编辑器',
          '材质编辑器节点，提供完整的材质编辑功能',
          NodeCategory.RENDERING,
          MaterialEditorNode,
          {
            icon: 'material-editor',
            color: '#E67E22',
            tags: ['material', 'editor', 'shader', 'texture']
          }
        ),
        createNodeInfo(
          'TextureBlending',
          '纹理混合',
          '纹理混合节点，支持多层纹理混合',
          NodeCategory.RENDERING,
          TextureBlendingNode,
          {
            icon: 'texture-blend',
            color: '#E67E22',
            tags: ['texture', 'blending', 'layer', 'mix']
          }
        ),
        createNodeInfo(
          'MaterialLibrary',
          '材质库',
          '材质库节点，管理材质资源和预设',
          NodeCategory.RENDERING,
          MaterialLibraryNode,
          {
            icon: 'material-library',
            color: '#E67E22',
            tags: ['material', 'library', 'preset', 'resource']
          }
        ),
        createNodeInfo(
          'PBRMaterial',
          'PBR材质',
          'PBR材质节点，基于物理的渲染材质',
          NodeCategory.RENDERING,
          PBRMaterialNode,
          {
            icon: 'pbr-material',
            color: '#E67E22',
            tags: ['pbr', 'material', 'physical', 'rendering']
          }
        ),
        createNodeInfo(
          'MaterialPreview',
          '材质预览',
          '材质预览节点，实时预览材质效果',
          NodeCategory.RENDERING,
          MaterialPreviewNode,
          {
            icon: 'material-preview',
            color: '#E67E22',
            tags: ['material', 'preview', 'realtime', 'render']
          }
        ),
        createNodeInfo(
          'MaterialOptimization',
          '材质优化',
          '材质优化节点，优化材质性能和质量',
          NodeCategory.RENDERING,
          MaterialOptimizationNode,
          {
            icon: 'material-optimize',
            color: '#E67E22',
            tags: ['material', 'optimization', 'performance', 'quality']
          }
        ),
        createNodeInfo(
          'MaterialVariant',
          '材质变体',
          '材质变体节点，创建材质的不同变体',
          NodeCategory.RENDERING,
          MaterialVariantNode,
          {
            icon: 'material-variant',
            color: '#E67E22',
            tags: ['material', 'variant', 'version', 'alternative']
          }
        ),
        createNodeInfo(
          'MaterialAnimation',
          '材质动画',
          '材质动画节点，创建动态材质效果',
          NodeCategory.RENDERING,
          MaterialAnimationNode,
          {
            icon: 'material-animation',
            color: '#E67E22',
            tags: ['material', 'animation', 'dynamic', 'effect']
          }
        ),
        createNodeInfo(
          'MaterialParameter',
          '材质参数',
          '材质参数节点，控制材质属性参数',
          NodeCategory.RENDERING,
          MaterialParameterNode,
          {
            icon: 'material-parameter',
            color: '#E67E22',
            tags: ['material', 'parameter', 'property', 'control']
          }
        ),
        createNodeInfo(
          'MaterialTemplate',
          '材质模板',
          '材质模板节点，使用预定义材质模板',
          NodeCategory.RENDERING,
          MaterialTemplateNode,
          {
            icon: 'material-template',
            color: '#E67E22',
            tags: ['material', 'template', 'preset', 'predefined']
          }
        )
      ];

      // 注册节点
      for (const nodeInfo of materialNodes) {
        this.registry.registerNode(nodeInfo);
        this.registeredNodeCount++;
      }

      Debug.log('ContentCreationNodesRegistry', `材质编辑节点注册完成: ${materialNodes.length}个节点`);

    } catch (error) {
      const errorMsg = `材质编辑节点注册失败: ${error}`;
      this.registrationErrors.push(errorMsg);
      Debug.error('ContentCreationNodesRegistry', errorMsg);
    }
  }

  /**
   * 注册其他创作工具节点（4个）
   */
  private async registerOtherCreationNodes(): Promise<void> {
    try {
      const otherNodes = [
        createNodeInfo(
          'AssetBrowser',
          '资源浏览器',
          '资源浏览器节点，提供资源管理和浏览功能',
          NodeCategory.RESOURCE_MANAGEMENT,
          AssetBrowserNode,
          {
            icon: 'asset-browser',
            color: '#95A5A6',
            tags: ['asset', 'browser', 'resource', 'management']
          }
        ),
        createNodeInfo(
          'ContentValidation',
          '内容验证',
          '内容验证节点，检查内容完整性和规范性',
          NodeCategory.RESOURCE_MANAGEMENT,
          ContentValidationNode,
          {
            icon: 'content-validation',
            color: '#95A5A6',
            tags: ['content', 'validation', 'check', 'integrity']
          }
        ),
        createNodeInfo(
          'AssetOptimization',
          '资源优化',
          '资源优化节点，优化资源文件大小和性能',
          NodeCategory.RESOURCE_MANAGEMENT,
          AssetOptimizationNode,
          {
            icon: 'asset-optimization',
            color: '#95A5A6',
            tags: ['asset', 'optimization', 'performance', 'compression']
          }
        ),
        createNodeInfo(
          'ContentExport',
          '内容导出',
          '内容导出节点，支持多种格式的内容导出',
          NodeCategory.RESOURCE_MANAGEMENT,
          ContentExportNode,
          {
            icon: 'content-export',
            color: '#95A5A6',
            tags: ['content', 'export', 'format', 'output']
          }
        )
      ];

      // 注册节点
      for (const nodeInfo of otherNodes) {
        this.registry.registerNode(nodeInfo);
        this.registeredNodeCount++;
      }

      Debug.log('ContentCreationNodesRegistry', `其他创作工具节点注册完成: ${otherNodes.length}个节点`);

    } catch (error) {
      const errorMsg = `其他创作工具节点注册失败: ${error}`;
      this.registrationErrors.push(errorMsg);
      Debug.error('ContentCreationNodesRegistry', errorMsg);
    }
  }

  /**
   * 获取已注册节点数量
   */
  public getRegisteredNodeCount(): number {
    return this.registeredNodeCount;
  }

  /**
   * 获取注册错误列表
   */
  public getRegistrationErrors(): string[] {
    return [...this.registrationErrors];
  }
}

// 创建全局实例
export const contentCreationNodesRegistry = ContentCreationNodesRegistry.getInstance();
