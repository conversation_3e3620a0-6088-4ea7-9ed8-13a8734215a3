/**
 * 路径编辑节点扩展实现
 * 提供样条曲线、贝塞尔曲线、路径验证等高级功能
 */

import { VisualScriptNode } from '../VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3, CubicBezierCurve3, CatmullRomCurve3 } from 'three';
import { PathConfig, PathPoint } from './PathEditingNodes';

/**
 * 样条曲线编辑器节点
 */
export class SplineEditorNode extends VisualScriptNode {
  public static readonly TYPE = 'SplineEditor';
  public static readonly NAME = '样条曲线编辑器';
  public static readonly DESCRIPTION = '编辑样条曲线路径，支持平滑曲线生成';

  constructor(nodeType: string = SplineEditorNode.TYPE, name: string = SplineEditorNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInputPort('createSpline', 'trigger', '创建样条');
    this.addInputPort('editSpline', 'trigger', '编辑样条');
    this.addInputPort('controlPoints', 'array', '控制点');
    this.addInputPort('tension', 'number', '张力');
    this.addInputPort('resolution', 'number', '分辨率');
    this.addInputPort('splineType', 'string', '样条类型');

    // 输出端口
    this.addOutputPort('splinePath', 'object', '样条路径');
    this.addOutputPort('curvePoints', 'array', '曲线点');
    this.addOutputPort('curveLength', 'number', '曲线长度');
    this.addOutputPort('onSplineCreated', 'trigger', '样条创建');
    this.addOutputPort('onSplineEdited', 'trigger', '样条编辑');
  }

  public execute(inputs?: any): any {
    try {
      if (inputs?.createSpline) {
        return this.createSpline(inputs);
      } else if (inputs?.editSpline) {
        return this.editSpline(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('SplineEditorNode', '执行失败', error);
      return this.getDefaultOutputs();
    }
  }

  private createSpline(inputs: any): any {
    const controlPoints = inputs?.controlPoints as Vector3[] || [];
    const tension = inputs?.tension as number || 0.5;
    const resolution = inputs?.resolution as number || 50;
    const splineType = inputs?.splineType as string || 'catmull-rom';

    if (controlPoints.length < 2) {
      Debug.warn('SplineEditorNode', '控制点数量不足');
      return this.getDefaultOutputs();
    }

    let curve: any;
    let curvePoints: Vector3[] = [];

    if (splineType === 'catmull-rom') {
      curve = new CatmullRomCurve3(controlPoints);
      curvePoints = curve.getPoints(resolution);
    } else {
      // 线性插值
      curvePoints = this.linearInterpolation(controlPoints, resolution);
    }

    const curveLength = this.calculateCurveLength(curvePoints);

    const splinePath: PathConfig = {
      id: `spline_${Date.now()}`,
      name: `Spline_${splineType}`,
      points: controlPoints.map((point, index) => ({
        id: `control_${index}`,
        position: point,
        type: 'smooth' as any
      })),
      closed: false,
      interpolationType: splineType as any,
      tension,
      resolution
    };

    Debug.log('SplineEditorNode', `样条曲线创建成功: ${splineType}`);

    return {
      splinePath,
      curvePoints,
      curveLength,
      onSplineCreated: true,
      onSplineEdited: false
    };
  }

  private editSpline(inputs: any): any {
    const controlPoints = inputs?.controlPoints as Vector3[] || [];
    const tension = inputs?.tension as number || 0.5;
    const resolution = inputs?.resolution as number || 50;

    if (controlPoints.length < 2) {
      Debug.warn('SplineEditorNode', '控制点数量不足');
      return this.getDefaultOutputs();
    }

    const curve = new CatmullRomCurve3(controlPoints);
    curve.tension = tension;
    const curvePoints = curve.getPoints(resolution);
    const curveLength = this.calculateCurveLength(curvePoints);

    Debug.log('SplineEditorNode', '样条曲线编辑完成');

    return {
      splinePath: null,
      curvePoints,
      curveLength,
      onSplineCreated: false,
      onSplineEdited: true
    };
  }

  private linearInterpolation(points: Vector3[], resolution: number): Vector3[] {
    const result: Vector3[] = [];
    
    for (let i = 0; i < points.length - 1; i++) {
      const start = points[i];
      const end = points[i + 1];
      
      for (let j = 0; j < resolution; j++) {
        const t = j / resolution;
        const interpolated = new Vector3().lerpVectors(start, end, t);
        result.push(interpolated);
      }
    }
    
    result.push(points[points.length - 1]);
    return result;
  }

  private calculateCurveLength(points: Vector3[]): number {
    let length = 0;
    for (let i = 1; i < points.length; i++) {
      length += points[i].distanceTo(points[i - 1]);
    }
    return length;
  }

  private getDefaultOutputs(): any {
    return {
      splinePath: null,
      curvePoints: [],
      curveLength: 0,
      onSplineCreated: false,
      onSplineEdited: false
    };
  }
}

/**
 * 贝塞尔曲线编辑器节点
 */
export class BezierCurveEditorNode extends VisualScriptNode {
  public static readonly TYPE = 'BezierCurveEditor';
  public static readonly NAME = '贝塞尔曲线编辑器';
  public static readonly DESCRIPTION = '编辑贝塞尔曲线，支持精确的曲线控制';

  constructor(nodeType: string = BezierCurveEditorNode.TYPE, name: string = BezierCurveEditorNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInputPort('createBezier', 'trigger', '创建贝塞尔');
    this.addInputPort('editBezier', 'trigger', '编辑贝塞尔');
    this.addInputPort('startPoint', 'vector3', '起始点');
    this.addInputPort('endPoint', 'vector3', '结束点');
    this.addInputPort('controlPoint1', 'vector3', '控制点1');
    this.addInputPort('controlPoint2', 'vector3', '控制点2');
    this.addInputPort('resolution', 'number', '分辨率');

    // 输出端口
    this.addOutputPort('bezierPath', 'object', '贝塞尔路径');
    this.addOutputPort('curvePoints', 'array', '曲线点');
    this.addOutputPort('tangents', 'array', '切线');
    this.addOutputPort('onBezierCreated', 'trigger', '贝塞尔创建');
    this.addOutputPort('onBezierEdited', 'trigger', '贝塞尔编辑');
  }

  public execute(inputs?: any): any {
    try {
      if (inputs?.createBezier) {
        return this.createBezier(inputs);
      } else if (inputs?.editBezier) {
        return this.editBezier(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('BezierCurveEditorNode', '执行失败', error);
      return this.getDefaultOutputs();
    }
  }

  private createBezier(inputs: any): any {
    const startPoint = inputs?.startPoint as Vector3 || new Vector3(0, 0, 0);
    const endPoint = inputs?.endPoint as Vector3 || new Vector3(1, 0, 0);
    const controlPoint1 = inputs?.controlPoint1 as Vector3 || new Vector3(0.33, 0.33, 0);
    const controlPoint2 = inputs?.controlPoint2 as Vector3 || new Vector3(0.67, 0.33, 0);
    const resolution = inputs?.resolution as number || 50;

    const curve = new CubicBezierCurve3(startPoint, controlPoint1, controlPoint2, endPoint);
    const curvePoints = curve.getPoints(resolution);
    const tangents = curve.getTangents(resolution);

    const bezierPath: PathConfig = {
      id: `bezier_${Date.now()}`,
      name: 'Bezier_Curve',
      points: [
        { id: 'start', position: startPoint, type: 'bezier', tangentOut: controlPoint1 },
        { id: 'end', position: endPoint, type: 'bezier', tangentIn: controlPoint2 }
      ],
      closed: false,
      interpolationType: 'cubic',
      tension: 0.5,
      resolution
    };

    Debug.log('BezierCurveEditorNode', '贝塞尔曲线创建成功');

    return {
      bezierPath,
      curvePoints,
      tangents,
      onBezierCreated: true,
      onBezierEdited: false
    };
  }

  private editBezier(inputs: any): any {
    const startPoint = inputs?.startPoint as Vector3;
    const endPoint = inputs?.endPoint as Vector3;
    const controlPoint1 = inputs?.controlPoint1 as Vector3;
    const controlPoint2 = inputs?.controlPoint2 as Vector3;
    const resolution = inputs?.resolution as number || 50;

    if (!startPoint || !endPoint || !controlPoint1 || !controlPoint2) {
      Debug.warn('BezierCurveEditorNode', '缺少必要的控制点');
      return this.getDefaultOutputs();
    }

    const curve = new CubicBezierCurve3(startPoint, controlPoint1, controlPoint2, endPoint);
    const curvePoints = curve.getPoints(resolution);
    const tangents = curve.getTangents(resolution);

    Debug.log('BezierCurveEditorNode', '贝塞尔曲线编辑完成');

    return {
      bezierPath: null,
      curvePoints,
      tangents,
      onBezierCreated: false,
      onBezierEdited: true
    };
  }

  private getDefaultOutputs(): any {
    return {
      bezierPath: null,
      curvePoints: [],
      tangents: [],
      onBezierCreated: false,
      onBezierEdited: false
    };
  }
}

/**
 * 路径插值节点
 */
export class PathInterpolationNode extends VisualScriptNode {
  public static readonly TYPE = 'PathInterpolation';
  public static readonly NAME = '路径插值';
  public static readonly DESCRIPTION = '设置路径插值方式，控制路径平滑度';

  constructor(nodeType: string = PathInterpolationNode.TYPE, name: string = PathInterpolationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInputPort('interpolate', 'trigger', '执行插值');
    this.addInputPort('path', 'object', '路径对象');
    this.addInputPort('interpolationType', 'string', '插值类型');
    this.addInputPort('smoothness', 'number', '平滑度');
    this.addInputPort('resolution', 'number', '分辨率');

    // 输出端口
    this.addOutputPort('interpolatedPath', 'object', '插值路径');
    this.addOutputPort('interpolatedPoints', 'array', '插值点');
    this.addOutputPort('onInterpolated', 'trigger', '插值完成');
  }

  public execute(inputs?: any): any {
    try {
      if (inputs?.interpolate) {
        return this.interpolatePath(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('PathInterpolationNode', '执行失败', error);
      return this.getDefaultOutputs();
    }
  }

  private interpolatePath(inputs: any): any {
    const path = inputs?.path as PathConfig;
    const interpolationType = inputs?.interpolationType as string || 'linear';
    const smoothness = inputs?.smoothness as number || 0.5;
    const resolution = inputs?.resolution as number || 50;

    if (!path || path.points.length < 2) {
      Debug.warn('PathInterpolationNode', '路径无效或点数不足');
      return this.getDefaultOutputs();
    }

    let interpolatedPoints: Vector3[] = [];

    switch (interpolationType) {
      case 'linear':
        interpolatedPoints = this.linearInterpolation(path.points, resolution);
        break;
      case 'cubic':
        interpolatedPoints = this.cubicInterpolation(path.points, resolution, smoothness);
        break;
      case 'catmull-rom':
        interpolatedPoints = this.catmullRomInterpolation(path.points, resolution);
        break;
      default:
        interpolatedPoints = this.linearInterpolation(path.points, resolution);
    }

    const interpolatedPath: PathConfig = {
      ...path,
      interpolationType: interpolationType as any,
      resolution,
      tension: smoothness
    };

    Debug.log('PathInterpolationNode', `路径插值完成: ${interpolationType}`);

    return {
      interpolatedPath,
      interpolatedPoints,
      onInterpolated: true
    };
  }

  private linearInterpolation(points: PathPoint[], resolution: number): Vector3[] {
    const result: Vector3[] = [];
    
    for (let i = 0; i < points.length - 1; i++) {
      const start = points[i].position;
      const end = points[i + 1].position;
      
      for (let j = 0; j < resolution; j++) {
        const t = j / resolution;
        const interpolated = new Vector3().lerpVectors(start, end, t);
        result.push(interpolated);
      }
    }
    
    result.push(points[points.length - 1].position);
    return result;
  }

  private cubicInterpolation(points: PathPoint[], resolution: number, smoothness: number): Vector3[] {
    // 简化的三次插值实现
    const positions = points.map(p => p.position);
    const curve = new CatmullRomCurve3(positions);
    curve.tension = smoothness;
    return curve.getPoints(resolution * (points.length - 1));
  }

  private catmullRomInterpolation(points: PathPoint[], resolution: number): Vector3[] {
    const positions = points.map(p => p.position);
    const curve = new CatmullRomCurve3(positions);
    return curve.getPoints(resolution * (points.length - 1));
  }

  private getDefaultOutputs(): any {
    return {
      interpolatedPath: null,
      interpolatedPoints: [],
      onInterpolated: false
    };
  }
}

/**
 * 路径验证节点
 */
export class PathValidationNode extends VisualScriptNode {
  public static readonly TYPE = 'PathValidation';
  public static readonly NAME = '路径验证';
  public static readonly DESCRIPTION = '验证路径有效性，检查路径完整性';

  constructor(nodeType: string = PathValidationNode.TYPE, name: string = PathValidationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInputPort('validate', 'trigger', '验证路径');
    this.addInputPort('path', 'object', '路径对象');
    this.addInputPort('strictMode', 'boolean', '严格模式');

    // 输出端口
    this.addOutputPort('isValid', 'boolean', '是否有效');
    this.addOutputPort('errors', 'array', '错误列表');
    this.addOutputPort('warnings', 'array', '警告列表');
    this.addOutputPort('onValidated', 'trigger', '验证完成');
  }

  public execute(inputs?: any): any {
    try {
      if (inputs?.validate) {
        return this.validatePath(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('PathValidationNode', '执行失败', error);
      return this.getDefaultOutputs();
    }
  }

  private validatePath(inputs: any): any {
    const path = inputs?.path as PathConfig;
    const strictMode = inputs?.strictMode as boolean || false;

    if (!path) {
      return {
        isValid: false,
        errors: ['路径对象为空'],
        warnings: [],
        onValidated: true
      };
    }

    const errors: string[] = [];
    const warnings: string[] = [];

    // 基本验证
    if (!path.points || path.points.length < 2) {
      errors.push('路径点数量不足（至少需要2个点）');
    }

    if (!path.id || path.id.trim() === '') {
      errors.push('路径ID不能为空');
    }

    if (!path.name || path.name.trim() === '') {
      warnings.push('路径名称为空');
    }

    // 严格模式验证
    if (strictMode) {
      // 检查点之间的距离
      for (let i = 1; i < path.points.length; i++) {
        const distance = path.points[i].position.distanceTo(path.points[i - 1].position);
        if (distance < 0.001) {
          warnings.push(`点 ${i} 和点 ${i - 1} 距离过近`);
        }
      }

      // 检查重复点
      const uniquePositions = new Set();
      path.points.forEach((point, index) => {
        const posKey = `${point.position.x},${point.position.y},${point.position.z}`;
        if (uniquePositions.has(posKey)) {
          warnings.push(`点 ${index} 位置重复`);
        }
        uniquePositions.add(posKey);
      });
    }

    const isValid = errors.length === 0;

    Debug.log('PathValidationNode', `路径验证完成: ${isValid ? '有效' : '无效'}`);

    return {
      isValid,
      errors,
      warnings,
      onValidated: true
    };
  }

  private getDefaultOutputs(): any {
    return {
      isValid: false,
      errors: [],
      warnings: [],
      onValidated: false
    };
  }
}
