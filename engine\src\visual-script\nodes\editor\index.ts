/**
 * 编辑器工具扩展节点模块导出
 * 批次6：编辑器工具扩展（32个节点）
 */

// 批次6.1: 编辑器界面节点（12个节点）
export {
  ToolbarManagerNode,
  ToolbarButtonNode,
  ToolbarGroupNode,
  ToolbarSeparatorNode,
  PanelManagerNode,
  PanelContentNode,
  PanelTabNode,
  PanelLayoutNode,
  MenuSystemNode,
  MenuItemNode,
  StatusBarNode,
  StatusBarItemNode
} from './EditorToolsExtensionNodes';

// 批次6.2: 资源管理节点（10个节点）
export {
  ResourceImporterNode,
  ResourceBatchImporterNode,
  ResourceFormatConverterNode,
  ResourceBatchConverterNode,
  ResourcePreviewerNode,
  ResourceValidatorNode,
  ResourceOptimizerNode
} from './EditorToolsExtensionNodes';

export {
  ResourceCacheManagerNode,
  ResourceCacheOperationNode,
  ResourceCacheStatsNode
} from './EditorToolsExtensionNodes2';

// 批次6.3: 调试工具节点（10个节点）
export {
  PerformanceProfilerNode,
  MemoryProfilerNode,
  CPUProfilerNode,
  RenderProfilerNode,
  DebugInfoDisplayNode
} from './EditorToolsExtensionNodes2';

export {
  DebugConsoleNode,
  DebugBreakpointNode,
  LogSystemManagerNode,
  LogFilterNode,
  LogExportNode
} from './EditorToolsExtensionNodes3';

// 节点类型常量
export const EDITOR_TOOLS_EXTENSION_NODE_TYPES = {
  // 编辑器界面节点
  TOOLBAR_MANAGER: 'ToolbarManager',
  TOOLBAR_BUTTON: 'ToolbarButton',
  TOOLBAR_GROUP: 'ToolbarGroup',
  TOOLBAR_SEPARATOR: 'ToolbarSeparator',
  PANEL_MANAGER: 'PanelManager',
  PANEL_CONTENT: 'PanelContent',
  PANEL_TAB: 'PanelTab',
  PANEL_LAYOUT: 'PanelLayout',
  MENU_SYSTEM: 'MenuSystem',
  MENU_ITEM: 'MenuItem',
  STATUS_BAR: 'StatusBar',
  STATUS_BAR_ITEM: 'StatusBarItem',

  // 资源管理节点
  RESOURCE_IMPORTER: 'ResourceImporter',
  RESOURCE_BATCH_IMPORTER: 'ResourceBatchImporter',
  RESOURCE_FORMAT_CONVERTER: 'ResourceFormatConverter',
  RESOURCE_BATCH_CONVERTER: 'ResourceBatchConverter',
  RESOURCE_PREVIEWER: 'ResourcePreviewer',
  RESOURCE_VALIDATOR: 'ResourceValidator',
  RESOURCE_OPTIMIZER: 'ResourceOptimizer',
  RESOURCE_CACHE_MANAGER: 'ResourceCacheManager',
  RESOURCE_CACHE_OPERATION: 'ResourceCacheOperation',
  RESOURCE_CACHE_STATS: 'ResourceCacheStats',

  // 调试工具节点
  PERFORMANCE_PROFILER: 'PerformanceProfiler',
  MEMORY_PROFILER: 'MemoryProfiler',
  CPU_PROFILER: 'CPUProfiler',
  RENDER_PROFILER: 'RenderProfiler',
  DEBUG_INFO_DISPLAY: 'DebugInfoDisplay',
  DEBUG_CONSOLE: 'DebugConsole',
  DEBUG_BREAKPOINT: 'DebugBreakpoint',
  LOG_SYSTEM_MANAGER: 'LogSystemManager',
  LOG_FILTER: 'LogFilter',
  LOG_EXPORT: 'LogExport'
} as const;

// 节点分类
export const EDITOR_TOOLS_EXTENSION_CATEGORIES = {
  EDITOR_INTERFACE: 'Editor/Interface',
  RESOURCE_MANAGEMENT: 'Editor/Resources',
  DEBUG_TOOLS: 'Editor/Debug'
} as const;

// 节点统计
export const EDITOR_TOOLS_EXTENSION_STATS = {
  TOTAL_NODES: 32,
  INTERFACE_NODES: 12,
  RESOURCE_NODES: 10,
  DEBUG_NODES: 10
} as const;
