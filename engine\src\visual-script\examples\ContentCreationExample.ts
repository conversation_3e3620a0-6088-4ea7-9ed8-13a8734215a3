/**
 * 批次4：内容创作工具节点使用示例
 * 展示如何使用41个内容创作工具节点进行内容创作
 */

import { Debug } from '../../utils/Debug';
import { NodeRegistry } from '../registry/NodeRegistry';
import { registerContentCreationNodes } from '../registry/NodeRegistry';
import { Vector3 } from 'three';

/**
 * 内容创作工具使用示例类
 */
export class ContentCreationExample {
  private nodeRegistry: NodeRegistry;

  constructor() {
    this.nodeRegistry = NodeRegistry.getInstance();
  }

  /**
   * 初始化示例
   */
  public async initialize(): Promise<void> {
    Debug.log('ContentCreationExample', '初始化内容创作工具示例...');
    
    // 注册内容创作工具节点
    registerContentCreationNodes();
    
    // 等待注册完成
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    Debug.log('ContentCreationExample', '内容创作工具节点注册完成');
  }

  /**
   * 运行所有示例
   */
  public async runAllExamples(): Promise<void> {
    Debug.log('ContentCreationExample', '开始内容创作工具示例演示...');

    try {
      await this.initialize();

      // 动画编辑示例
      await this.animationEditingExample();
      
      // 路径编辑示例
      await this.pathEditingExample();
      
      // 材质编辑示例
      await this.materialEditingExample();
      
      // 资源管理示例
      await this.assetManagementExample();

      Debug.log('ContentCreationExample', '内容创作工具示例演示完成 ✅');

    } catch (error) {
      Debug.error('ContentCreationExample', '示例演示失败:', error);
      throw error;
    }
  }

  /**
   * 动画编辑示例
   */
  private async animationEditingExample(): Promise<void> {
    Debug.log('ContentCreationExample', '=== 动画编辑示例 ===');

    try {
      // 创建动画时间轴节点
      const timelineNode = this.nodeRegistry.createNode('AnimationTimeline');
      if (timelineNode) {
        const result = timelineNode.execute({
          createTimeline: true,
          duration: 10.0,
          frameRate: 30
        });
        Debug.log('ContentCreationExample', '动画时间轴创建:', result);
      }

      // 创建关键帧编辑器节点
      const keyframeNode = this.nodeRegistry.createNode('KeyframeEditor');
      if (keyframeNode) {
        const result = keyframeNode.execute({
          addKeyframe: true,
          time: 2.5,
          value: new Vector3(1, 0, 0),
          interpolation: 'bezier'
        });
        Debug.log('ContentCreationExample', '关键帧添加:', result);
      }

      // 创建动画混合节点
      const blendingNode = this.nodeRegistry.createNode('AnimationBlending');
      if (blendingNode) {
        const result = blendingNode.execute({
          blend: true,
          sourceAnimation: 'walk',
          targetAnimation: 'run',
          blendFactor: 0.5,
          blendDuration: 1.0
        });
        Debug.log('ContentCreationExample', '动画混合:', result);
      }

      Debug.log('ContentCreationExample', '动画编辑示例完成 ✅');

    } catch (error) {
      Debug.error('ContentCreationExample', '动画编辑示例失败:', error);
    }
  }

  /**
   * 路径编辑示例
   */
  private async pathEditingExample(): Promise<void> {
    Debug.log('ContentCreationExample', '=== 路径编辑示例 ===');

    try {
      // 创建路径创建节点
      const pathCreationNode = this.nodeRegistry.createNode('PathCreation');
      if (pathCreationNode) {
        const result = pathCreationNode.execute({
          create: true,
          pathName: 'ExamplePath',
          pathType: 'cubic',
          startPoint: new Vector3(0, 0, 0),
          endPoint: new Vector3(10, 0, 0),
          closed: false
        });
        Debug.log('ContentCreationExample', '路径创建:', result);
      }

      // 创建样条曲线编辑器节点
      const splineNode = this.nodeRegistry.createNode('SplineEditor');
      if (splineNode) {
        const controlPoints = [
          new Vector3(0, 0, 0),
          new Vector3(3, 2, 0),
          new Vector3(7, 1, 0),
          new Vector3(10, 0, 0)
        ];
        
        const result = splineNode.execute({
          createSpline: true,
          controlPoints,
          tension: 0.5,
          resolution: 50,
          splineType: 'catmull-rom'
        });
        Debug.log('ContentCreationExample', '样条曲线创建:', result);
      }

      // 创建路径跟随节点
      const pathFollowNode = this.nodeRegistry.createNode('PathFollowing');
      if (pathFollowNode) {
        const mockTarget = { name: 'TestObject', position: new Vector3() };
        const mockPath = { id: 'path1', points: [], closed: false };
        
        const result = pathFollowNode.execute({
          startFollow: true,
          target: mockTarget,
          path: mockPath,
          speed: 2.0,
          loop: true,
          lookAhead: true
        });
        Debug.log('ContentCreationExample', '路径跟随:', result);
      }

      Debug.log('ContentCreationExample', '路径编辑示例完成 ✅');

    } catch (error) {
      Debug.error('ContentCreationExample', '路径编辑示例失败:', error);
    }
  }

  /**
   * 材质编辑示例
   */
  private async materialEditingExample(): Promise<void> {
    Debug.log('ContentCreationExample', '=== 材质编辑示例 ===');

    try {
      // 创建材质模板节点
      const templateNode = this.nodeRegistry.createNode('MaterialTemplate');
      if (templateNode) {
        const result = templateNode.execute({
          applyTemplate: true,
          templateName: '金属',
          material: { type: 'MeshStandardMaterial', color: 0xffffff }
        });
        Debug.log('ContentCreationExample', '材质模板应用:', result);
      }

      // 创建材质参数节点
      const parameterNode = this.nodeRegistry.createNode('MaterialParameter');
      if (parameterNode) {
        const result = parameterNode.execute({
          setParameter: true,
          material: { type: 'MeshStandardMaterial', metalness: 0.5 },
          parameterName: 'metalness',
          parameterValue: 0.8
        });
        Debug.log('ContentCreationExample', '材质参数设置:', result);
      }

      // 创建材质优化节点
      const optimizationNode = this.nodeRegistry.createNode('MaterialOptimization');
      if (optimizationNode) {
        const result = optimizationNode.execute({
          optimize: true,
          material: { type: 'MeshStandardMaterial' },
          optimizationLevel: 'medium',
          targetPlatform: 'mobile',
          preserveQuality: true
        });
        Debug.log('ContentCreationExample', '材质优化:', result);
      }

      Debug.log('ContentCreationExample', '材质编辑示例完成 ✅');

    } catch (error) {
      Debug.error('ContentCreationExample', '材质编辑示例失败:', error);
    }
  }

  /**
   * 资源管理示例
   */
  private async assetManagementExample(): Promise<void> {
    Debug.log('ContentCreationExample', '=== 资源管理示例 ===');

    try {
      // 创建资源浏览器节点
      const browserNode = this.nodeRegistry.createNode('AssetBrowser');
      if (browserNode) {
        const result = browserNode.execute({
          browse: true,
          path: '/models',
          filterType: 'model'
        });
        Debug.log('ContentCreationExample', '资源浏览:', result);
      }

      // 创建内容验证节点
      const validationNode = this.nodeRegistry.createNode('ContentValidation');
      if (validationNode) {
        const testContent = {
          id: 'test_001',
          name: 'TestAsset',
          type: 'model',
          metadata: { version: '1.0' }
        };
        
        const result = validationNode.execute({
          validate: true,
          content: testContent,
          validationRules: ['required_name', 'required_id', 'valid_type'],
          strictMode: true
        });
        Debug.log('ContentCreationExample', '内容验证:', result);
      }

      // 创建资源优化节点
      const assetOptimizationNode = this.nodeRegistry.createNode('AssetOptimization');
      if (assetOptimizationNode) {
        const testAsset = {
          id: 'asset_001',
          name: 'TestModel.fbx',
          type: 'model',
          size: 2048000,
          path: '/models/TestModel.fbx'
        };
        
        const result = assetOptimizationNode.execute({
          optimize: true,
          asset: testAsset,
          optimizationLevel: 'high',
          targetPlatform: 'mobile',
          preserveQuality: false
        });
        Debug.log('ContentCreationExample', '资源优化:', result);
      }

      // 创建内容导出节点
      const exportNode = this.nodeRegistry.createNode('ContentExport');
      if (exportNode) {
        const testContent = {
          name: 'ExportTest',
          data: { vertices: 1000, materials: 2 }
        };
        
        const result = exportNode.execute({
          export: true,
          content: testContent,
          format: 'json',
          outputPath: './exports/test_export',
          exportOptions: { compress: true }
        });
        Debug.log('ContentCreationExample', '内容导出:', result);
      }

      Debug.log('ContentCreationExample', '资源管理示例完成 ✅');

    } catch (error) {
      Debug.error('ContentCreationExample', '资源管理示例失败:', error);
    }
  }

  /**
   * 生成示例报告
   */
  public generateExampleReport(): string {
    return `
批次4：内容创作工具节点使用示例报告
=====================================

示例覆盖范围:
- 动画编辑功能: 时间轴、关键帧、混合
- 路径编辑功能: 创建、样条曲线、跟随
- 材质编辑功能: 模板、参数、优化
- 资源管理功能: 浏览、验证、优化、导出

节点使用统计:
- 动画编辑节点: 3/7 个节点演示
- 路径编辑节点: 3/18 个节点演示
- 材质编辑节点: 3/10 个节点演示
- 其他创作工具节点: 4/4 个节点演示

示例特点:
✅ 展示了节点的基本使用方法
✅ 演示了节点间的协作流程
✅ 提供了实际的参数配置示例
✅ 包含了错误处理机制

生成时间: ${new Date().toLocaleString()}
    `.trim();
  }
}

// 导出示例实例
export const contentCreationExample = new ContentCreationExample();

// 如果直接运行此文件，执行示例
if (require.main === module) {
  contentCreationExample.runAllExamples().then(() => {
    console.log(contentCreationExample.generateExampleReport());
  }).catch(error => {
    console.error('示例运行失败:', error);
  });
}
