/**
 * 验证批次5：基础系统扩展节点注册表
 * 简单的JavaScript验证脚本
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证批次5：基础系统扩展节点注册表...');
console.log('='.repeat(60));

try {
  // 1. 检查注册表文件是否存在
  const registryPath = path.join(__dirname, 'BasicSystemExtensionRegistry.ts');
  if (fs.existsSync(registryPath)) {
    console.log('✅ BasicSystemExtensionRegistry.ts 文件存在');
  } else {
    console.log('❌ BasicSystemExtensionRegistry.ts 文件不存在');
    process.exit(1);
  }

  // 2. 读取文件内容
  const registryContent = fs.readFileSync(registryPath, 'utf8');
  console.log(`✅ 文件大小: ${registryContent.length} 字符`);

  // 3. 检查关键类和方法
  const requiredElements = [
    'BasicSystemExtensionRegistry',
    'registerNodes',
    'registerRenderingExtensionNodes',
    'registerPhysicsExtensionNodes', 
    'registerAudioExtensionNodes',
    'registerInputExtensionNodes',
    'isRegistered',
    'getAllRegisteredNodeTypes',
    'getNodeStatistics'
  ];

  console.log('\n📋 检查关键元素:');
  let missingElements = [];
  
  for (const element of requiredElements) {
    if (registryContent.includes(element)) {
      console.log(`  ✅ ${element}`);
    } else {
      console.log(`  ❌ ${element} - 未找到`);
      missingElements.push(element);
    }
  }

  if (missingElements.length > 0) {
    console.log(`\n❌ 缺少 ${missingElements.length} 个关键元素`);
    process.exit(1);
  }

  // 4. 检查节点类型定义
  console.log('\n🎯 检查节点类型定义:');
  
  // 渲染系统扩展节点（15个）
  const renderingNodes = [
    'AdvancedVertexShader', 'AdvancedFragmentShader', 'TessellationShader', 
    'GeometryShader', 'ComputeShader', 'AdvancedBloom', 'MotionBlur', 
    'DepthOfField', 'ScreenSpaceReflection', 'VolumetricLighting',
    'GlobalIllumination', 'VolumetricFog', 'LightProbe', 
    'CascadedShadowMap', 'ContactShadow'
  ];

  // 物理系统扩展节点（12个）
  const physicsNodes = [
    'ContinuousCollisionDetection', 'ConvexHullCollision', 'MeshCollision', 
    'HeightFieldCollision', 'FluidSimulation', 'ParticleFluid', 
    'FluidInteraction', 'SoftBody', 'ClothSimulation', 'RopeSimulation',
    'AdvancedConstraint', 'ConstraintSolver'
  ];

  // 音频系统扩展节点（8个）
  const audioNodes = [
    'SpatialAudio', 'AudioOcclusion', 'ReverbZone', 'AudioFilter',
    'AudioDistortion', 'AudioChorus', 'AudioSpectrumAnalyzer', 'AudioBeatDetection'
  ];

  // 输入系统扩展节点（15个）
  const inputNodes = [
    'VRControllerInput', 'VRHeadsetTracking', 'ARTouchInput', 'ARGestureInput',
    'SpatialInput', 'EyeTrackingInput', 'HandGestureRecognition', 'FingerTracking',
    'PalmDetection', 'GestureClassification', 'SpeechRecognition', 'VoiceCommand',
    'SpeechToText', 'Accelerometer', 'Gyroscope'
  ];

  const allNodes = [...renderingNodes, ...physicsNodes, ...audioNodes, ...inputNodes];
  
  console.log(`  渲染系统扩展节点: ${renderingNodes.length}个`);
  console.log(`  物理系统扩展节点: ${physicsNodes.length}个`);
  console.log(`  音频系统扩展节点: ${audioNodes.length}个`);
  console.log(`  输入系统扩展节点: ${inputNodes.length}个`);
  console.log(`  总计: ${allNodes.length}个`);

  // 5. 检查节点注册调用
  let foundNodes = 0;
  for (const node of allNodes) {
    if (registryContent.includes(node)) {
      foundNodes++;
    }
  }

  console.log(`\n📊 节点注册检查: ${foundNodes}/${allNodes.length} 个节点已定义`);

  if (foundNodes === allNodes.length) {
    console.log('✅ 所有节点都已正确定义');
  } else {
    console.log(`⚠️ 有 ${allNodes.length - foundNodes} 个节点未找到定义`);
  }

  // 6. 检查NodeRegistrations.ts集成
  const nodeRegistrationsPath = path.join(__dirname, 'NodeRegistrations.ts');
  if (fs.existsSync(nodeRegistrationsPath)) {
    const nodeRegistrationsContent = fs.readFileSync(nodeRegistrationsPath, 'utf8');
    
    if (nodeRegistrationsContent.includes('registerBasicSystemExtensionNodes')) {
      console.log('✅ 已集成到NodeRegistrations.ts');
    } else {
      console.log('❌ 未集成到NodeRegistrations.ts');
    }
  }

  // 7. 检查文档文件
  const readmePath = path.join(__dirname, 'README_BasicSystemExtension.md');
  if (fs.existsSync(readmePath)) {
    console.log('✅ README_BasicSystemExtension.md 文档存在');
  } else {
    console.log('❌ README_BasicSystemExtension.md 文档不存在');
  }

  console.log('\n🎉 批次5：基础系统扩展节点注册表验证完成！');
  console.log('='.repeat(60));

} catch (error) {
  console.error('\n❌ 验证失败:', error.message);
  process.exit(1);
}
